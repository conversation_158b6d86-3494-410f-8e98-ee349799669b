@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TooGenericExceptionCaught", "MaxLineLength")

package com.superhexa.supervision.feature.miwear.speechhub.service

import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.superhexa.supervision.feature.miwear.speechhub.data.repository.SummaryDataRepository
import com.superhexa.supervision.feature.miwear.speechhub.data.retrofit.service.SummaryRetrofitFactory
import com.superhexa.supervision.feature.miwear.speechhub.data.retrofit.service.SummaryRetrofitService
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.db.bean.SummaryStatus
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import kotlin.coroutines.resume

/**
 * 录音总结管理器
 * 负责处理录音总结相关的所有逻辑，包括：
 * 1. 自动总结检查和启动
 * 2. 手动总结请求
 * 3. 总结结果轮询
 * 4. 总结状态管理
 * 5. 数据库状态更新
 */
class AudioSummaryManager(
    private val serviceScope: CoroutineScope,
    private val transcriptionCallback: TranscriptionStateListener
) {

    companion object {
        // 总结相关常量
        private const val POLLING_DELAY_MS = 3000L // 3秒轮询间隔
        private const val MAX_RETRY_ATTEMPTS = 20 // 最大重试次数
        private const val REQUEST_SUMMARY_ERROR = 10001
        private const val GET_SUMMARY_RESULT_ERROR = 10002
    }

    // 总结Repository
    private val summaryRepository = SummaryDataRepository(
        SummaryRetrofitFactory.provideService(SummaryRetrofitService::class.java)
    )

    // 当前正在进行的总结任务，key为summaryTaskId，value为转写taskId
    private val activeSummaries = ConcurrentHashMap<String, String>()

    // 总结上下文信息，用于数据库更新和轮询
    private val summaryContexts = ConcurrentHashMap<String, SummaryContext>()

    /**
     * 总结上下文数据类，包含总结轮询所需的所有信息
     */
    data class SummaryContext(
        val transcriptionTaskId: String,
        val summaryTaskId: String,
        val requestId: String,
        val filePath: String,
        val mediaBean: MediaBean?,
        val template: String,
        val transcribeContent: String,
        val token: String,
        val attempt: Int = 0
    )

    /**
     * 检查并开始自动总结
     */
    suspend fun checkAndStartAutoSummary(
        taskId: String,
        filePath: String,
        phrases: List<Phrase>,
        mediaBean: MediaBean,
        summaryTemplate: String
    ) {
        try {
            // 确保在IO线程中执行数据库查询，避免竞争条件
            val audioBean = withContext(Dispatchers.IO) {
                AudioTranscriptionDbHelper.findCorrespondBean(mediaBean).firstOrNull()
            }

            val shouldAutoSummary = audioBean?.summaryTaskId.isNullOrEmpty()
            Timber.i("检查是否需要自动总结: taskId=$taskId, shouldAutoSummary=$shouldAutoSummary, summaryTaskId=${audioBean?.summaryTaskId}")

            if (shouldAutoSummary && summaryTemplate.isNotEmpty()) {
                // 准备转写内容
                val transcribeContent = phrases.joinToString(separator = "\n") { phrase ->
                    "${phrase.speakerName}：\n${phrase.text}"
                }

                if (transcribeContent.isNotEmpty()) {
                    Timber.i("开始自动总结: taskId=$taskId, template=$summaryTemplate, content.length=${transcribeContent.length}")
                    startSummaryRequestSuspend(
                        taskId,
                        filePath,
                        mediaBean,
                        summaryTemplate,
                        transcribeContent
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "检查自动总结时发生错误: taskId=$taskId")
        }
    }

    /**
     * 手动请求总结（用于重新总结）
     */
    fun requestSummary(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        speakerFocusJson: String?,
        isReSummary: Boolean = false
    ) {
        Timber.i("手动请求总结: transcriptionTaskId=$transcriptionTaskId, isReSummary=$isReSummary")

        serviceScope.launch {
            try {
                if (isReSummary) {
                    // 清空现有总结内容
                    withContext(Dispatchers.IO) {
                        AudioTranscriptionDbHelper.clearSummary(mediaBean, template)
                    }
                }

                // 直接调用suspend版本，避免重复的协程启动
                startSummaryRequestSuspend(
                    transcriptionTaskId,
                    filePath,
                    mediaBean,
                    template,
                    transcribeContent,
                    speakerFocusJson
                )
            } catch (e: Exception) {
                Timber.e(e, "手动请求总结时发生错误: transcriptionTaskId=$transcriptionTaskId")
                transcriptionCallback.onSummaryFailed(
                    transcriptionTaskId,
                    filePath,
                    "",
                    REQUEST_SUMMARY_ERROR,
                    template
                )
            }
        }
    }

    /**
     * 开始总结请求（suspend版本，用于内部调用）
     */
    private suspend fun startSummaryRequestSuspend(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        speakerFocusJson: String? = null
    ) {
        try {
            // 获取AI Token（使用suspendCancellableCoroutine包装回调）
            val token = suspendCancellableCoroutine<String> { continuation ->
                AiCapabilityWrapper.INSTANCE.getToken { token ->
                    continuation.resume(token)
                }
            }

            // 直接调用实际的总结请求
            realRequestSummary(
                transcriptionTaskId,
                filePath,
                mediaBean,
                template,
                transcribeContent,
                speakerFocusJson,
                token
            )
        } catch (e: Exception) {
            Timber.e(e, "开始总结请求时发生错误: transcriptionTaskId=$transcriptionTaskId")
            transcriptionCallback.onSummaryFailed(
                transcriptionTaskId,
                filePath,
                "",
                REQUEST_SUMMARY_ERROR,
                template
            )
        }
    }

    /**
     * 实际执行总结请求
     */
    private suspend fun realRequestSummary(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        speakerFocusJson: String? = null,
        token: String
    ) {
        try {
            Timber.i("实际执行总结请求: transcriptionTaskId=$transcriptionTaskId, content.length=${transcribeContent.length}")

            val requestId = UUID.randomUUID().toString()

            summaryRepository.createSummaryTask(
                template,
                requestId,
                transcribeContent,
                token,
                speakerFocusJson
            ).collect { result ->
                when {
                    result.isSuccess() -> {
                        Timber.i("总结请求成功: ${result.data}")
                        result.data?.taskId?.let { summaryTaskId ->
                            // 更新数据库中的总结任务ID
                            // 首次总结的时候，这里的更新其实是无效的，需要在总结成功的时候也更新
                            withContext(Dispatchers.IO) {
                                AudioTranscriptionDbHelper.updateSummaryTaskId(
                                    mediaBean,
                                    summaryTaskId
                                )
                            }

                            // 创建总结上下文
                            val summaryContext = SummaryContext(
                                transcriptionTaskId = transcriptionTaskId,
                                summaryTaskId = summaryTaskId,
                                requestId = requestId,
                                filePath = filePath,
                                mediaBean = mediaBean,
                                template = template,
                                transcribeContent = transcribeContent,
                                token = token
                            )

                            summaryContexts[summaryTaskId] = summaryContext
                            activeSummaries[summaryTaskId] = transcriptionTaskId

                            // 通过transcriptionCallback通知总结开始（数据库更新在callback中处理）
                            transcriptionCallback.onSummaryStarted(
                                transcriptionTaskId,
                                filePath,
                                summaryTaskId,
                                template
                            )

                            // 开始轮询总结结果
                            startSummaryPolling(summaryTaskId)
                        }
                    }

                    result.isLoading() -> {
                        Timber.i("总结请求加载中")
                    }

                    result.isError() -> {
                        Timber.e("总结请求失败: code=${result.code}, message=${result.message}")
                        handleSummaryFailed(
                            transcriptionTaskId,
                            filePath,
                            "",
                            result.code ?: REQUEST_SUMMARY_ERROR,
                            template
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "执行总结请求时发生异常: transcriptionTaskId=$transcriptionTaskId")
            handleSummaryFailed(transcriptionTaskId, filePath, "", REQUEST_SUMMARY_ERROR, template)
        }
    }

    /**
     * 开始总结轮询
     */
    private fun startSummaryPolling(summaryTaskId: String) {
        serviceScope.launch {
            pollSummaryResult(summaryTaskId, 0)
        }
    }

    /**
     * 轮询总结结果
     */
    @Suppress("LongMethod")
    private suspend fun pollSummaryResult(summaryTaskId: String, attempt: Int) {
        val context = summaryContexts[summaryTaskId]
        if (context == null) {
            Timber.w("总结上下文不存在，停止轮询: summaryTaskId=$summaryTaskId")
            return
        }

        if (attempt >= MAX_RETRY_ATTEMPTS) {
            Timber.e("总结轮询达到最大重试次数: summaryTaskId=$summaryTaskId")
            handleSummaryFailed(
                context.transcriptionTaskId,
                context.filePath,
                summaryTaskId,
                GET_SUMMARY_RESULT_ERROR,
                context.template
            )
            return
        }

        try {
            summaryRepository.getSummaryResult(context.requestId, summaryTaskId, context.token)
                .collect { result ->
                    when {
                        result.isSuccess() -> {
                            Timber.i("总结轮询成功: summaryTaskId=$summaryTaskId, data=${result.data != null}")
                            result.data?.let { response ->
                                val title = response.title?.trim() ?: ""
                                val content = response.content ?: ""
                                val speakerFocusContent = response.speakerFocusContent.toJson()

                                // 更新数据库内容
                                withContext(Dispatchers.IO) {
                                    AudioTranscriptionDbHelper.updateSummaryContent(
                                        context.mediaBean!!,
                                        content,
                                        summaryTaskId,
                                        speakerFocusContent,
                                        summaryTitle = title,
                                        template = context.template,
                                        summaryErrorCode = 0
                                    )
                                }

                                // 更新MediaBean的fileName字段为总结标题并保存到数据库
                                withContext(Dispatchers.IO) {
                                    context.mediaBean?.let { mediaBean ->
                                        if (!mediaBean.isFileNameChangedByUser) {
                                            mediaBean.fileName = title
                                            DbHelper.updateMediaFileName(mediaBean)
                                            Timber.i("已更新MediaBean的fileName为总结标题: $title")
                                        } else {
                                            Timber.i("文件标题已被用户编辑，不再自动更新: $title")
                                        }
                                    }
                                }

                                // 通过transcriptionCallback通知总结成功（上下文清理将在回调中的异步操作完成后执行）
                                transcriptionCallback.onSummarySuccess(
                                    context.transcriptionTaskId,
                                    context.filePath,
                                    summaryTaskId,
                                    title,
                                    content,
                                    speakerFocusContent,
                                    context.template
                                )
                            }
                        }

                        result.isLoading() -> {
                            Timber.i("总结还在处理中，继续轮询: summaryTaskId=$summaryTaskId, attempt=$attempt")
                            // 延迟后继续轮询
                            delay(POLLING_DELAY_MS)
                            pollSummaryResult(summaryTaskId, attempt + 1)
                        }

                        result.isError() -> {
                            Timber.e("总结轮询失败: summaryTaskId=$summaryTaskId, code=${result.code}, message=${result.message}")
                            handleSummaryFailed(
                                context.transcriptionTaskId,
                                context.filePath,
                                summaryTaskId,
                                result.code ?: GET_SUMMARY_RESULT_ERROR,
                                context.template
                            )
                        }
                    }
                }
        } catch (e: Exception) {
            Timber.e(e, "总结轮询异常: summaryTaskId=$summaryTaskId, attempt=$attempt")
            // 延迟后重试
            delay(POLLING_DELAY_MS)
            pollSummaryResult(summaryTaskId, attempt + 1)
        }
    }

    /**
     * 处理总结失败
     */
    private suspend fun handleSummaryFailed(
        transcriptionTaskId: String,
        filePath: String,
        summaryTaskId: String,
        errorCode: Int,
        template: String
    ) {
        try {
            val context = summaryContexts[summaryTaskId]
            if (context?.mediaBean != null) {
                // 更新数据库记录失败状态
                withContext(Dispatchers.IO) {
                    AudioTranscriptionDbHelper.updateSummaryContent(
                        context.mediaBean,
                        "",
                        summaryTaskId,
                        null,
                        summaryTitle = "",
                        template = template,
                        summaryErrorCode = errorCode
                    )
                }
            }

            // 通过transcriptionCallback通知总结失败（上下文清理将在回调中的异步操作完成后执行）
            transcriptionCallback.onSummaryFailed(
                transcriptionTaskId,
                filePath,
                summaryTaskId,
                errorCode,
                template
            )
        } catch (e: Exception) {
            Timber.e(e, "处理总结失败时发生异常: summaryTaskId=$summaryTaskId")
        }
    }

    /**
     * 更新数据库中的总结状态（suspend版本，用于已在协程中的调用）
     */
    suspend fun updateDatabaseSummaryStatus(summaryTaskId: String, status: SummaryStatus) {
        val context = summaryContexts[summaryTaskId]
        if (context?.mediaBean != null) {
            withContext(Dispatchers.IO) {
                try {
                    Timber.i("后台服务更新总结状态: summaryTaskId=$summaryTaskId, status=${status.name}")
                    AudioTranscriptionDbHelper.updateSummaryStatus(context.mediaBean, status)
                    Timber.i("后台服务成功更新总结状态到数据库: summaryTaskId=$summaryTaskId, status=${status.name}")
                } catch (e: Exception) {
                    Timber.e(
                        e,
                        "更新总结状态到数据库失败: summaryTaskId=$summaryTaskId, status=${status.name}"
                    )
                }
            }
        } else {
            Timber.w("无法更新总结状态 - 缺少MediaBean: summaryTaskId=$summaryTaskId, status=${status.name}")
        }
    }

    /**
     * 清理总结上下文
     */
    fun clearSummaryContext(summaryTaskId: String) {
        summaryContexts.remove(summaryTaskId)
        activeSummaries.remove(summaryTaskId)
        Timber.i("清理总结上下文: summaryTaskId=$summaryTaskId")
    }

    /**
     * 清理所有总结上下文
     */
    fun clearAllSummaryContexts() {
        summaryContexts.clear()
        activeSummaries.clear()
        Timber.i("清理所有总结上下文")
    }

    /**
     * 获取活跃总结任务数量
     */
    fun getActiveSummaryCount(): Int {
        return activeSummaries.size
    }

    /**
     * 检查指定任务是否正在总结
     */
    fun isTaskSummarizing(summaryTaskId: String): Boolean {
        return activeSummaries.containsKey(summaryTaskId)
    }
}
