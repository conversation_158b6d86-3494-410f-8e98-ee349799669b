package com.superhexa.supervision.feature.profile.presentation.privacy

import androidx.annotation.Keep
import androidx.lifecycle.viewModelScope
import com.superhexa.supervision.feature.profile.R
import com.superhexa.supervision.feature.profile.data.repository.ServiceImprovementRepository
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.viewmodel.BaseViewModelEx
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber

class PrivacySetViewModel : BaseViewModelEx() {

    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val repository = ServiceImprovementRepository()

    private val _serviceSwitchState = MutableStateFlow<Boolean?>(null)
    val serviceSwitchState: StateFlow<Boolean?> = _serviceSwitchState

    private val _voiceWakeUpSwitchState = MutableStateFlow<Boolean?>(null)
    val voiceWakeUpState: StateFlow<Boolean?> = _voiceWakeUpSwitchState

    private val _speechRecognitionSwitchState = MutableStateFlow<Boolean?>(null)
    val speechRecognitionState: StateFlow<Boolean?> = _speechRecognitionSwitchState

    init {
        viewModelScope.launch(Dispatchers.IO) {
            _voiceWakeUpSwitchState.value = withContext(Dispatchers.IO) {
                MMKVUtils.decodeBoolean(ConstsConfig.UserVoiceWakeUpAgreement, true)
            }

            _speechRecognitionSwitchState.value = withContext(Dispatchers.IO) {
                MMKVUtils.decodeBoolean(ConstsConfig.UserSpeechRecognitionAgreement, true)
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    fun requestRemoteSwitchStatus(deviceId: String) {
        aiCapability.getToken {
            try {
                viewModelScope.launch(Dispatchers.IO) {
                    val result = repository.getServiceImprovementSwitch(deviceId, it)

                    if (result.isSuccess()) {
                        withContext(Dispatchers.Main) {
                            _serviceSwitchState.value =
                                result.data?.data?.privacy?.client_log_upload ?: true
                            _voiceWakeUpSwitchState.value =
                                result.data?.data?.privacy?.voice_wakeup ?: true
                            _speechRecognitionSwitchState.value =
                                result.data?.data?.privacy?.speech_recognition ?: true
                        }
                    } else {
                        LibBaseApplication.instance.toast(R.string.syncSwitchStatusFailed)
                    }

                    withContext(Dispatchers.IO) {
                        MMKVUtils.encode(
                            ConstsConfig.UserVoiceWakeUpAgreement,
                            _voiceWakeUpSwitchState.value
                        )
                        MMKVUtils.encode(
                            ConstsConfig.UserSpeechRecognitionAgreement,
                            _speechRecognitionSwitchState.value
                        )
                    }
                }
            } catch (e: Exception) {
                Timber.e("Improvement switch checked error")
            }
        }
    }

    @Suppress("TooGenericExceptionCaught", "ComplexMethod")
    fun updateRemoteSwitchStatus(type: SwitchType, checkStatus: Boolean, deviceId: String) {
        aiCapability.getToken {
            try {
                viewModelScope.launch {
                    val (first, second, third) = when (type) {
                        SwitchType.SERVICE_IMPROVEMENT -> Triple(
                            checkStatus,
                            voiceWakeUpState.value ?: true,
                            speechRecognitionState.value ?: true
                        )

                        SwitchType.VOICE_WAKEUP -> Triple(
                            serviceSwitchState.value ?: true,
                            checkStatus,
                            speechRecognitionState.value ?: true
                        )

                        SwitchType.SPEECH_RECOGNITION -> Triple(
                            serviceSwitchState.value ?: true,
                            voiceWakeUpState.value ?: true,
                            checkStatus
                        )
                    }

                    when (type) {
                        SwitchType.SERVICE_IMPROVEMENT ->
                            _serviceSwitchState.value = checkStatus

                        SwitchType.VOICE_WAKEUP -> {
                            _voiceWakeUpSwitchState.value = checkStatus
                            withContext(Dispatchers.IO) {
                                MMKVUtils.encode(
                                    ConstsConfig.UserVoiceWakeUpAgreement,
                                    checkStatus
                                )
                            }
                        }

                        SwitchType.SPEECH_RECOGNITION -> {
                            _speechRecognitionSwitchState.value = checkStatus
                            withContext(Dispatchers.IO) {
                                MMKVUtils.encode(
                                    ConstsConfig.UserSpeechRecognitionAgreement,
                                    checkStatus
                                )
                            }
                        }
                    }

                    repository.updateServiceImprovementSwitch(
                        isEnableUploadLog = first,
                        isEnableVoiceWakeUp = second,
                        isEnableSpeechRecognition = third,
                        deviceId,
                        it
                    )
                }
            } catch (e: Exception) {
                Timber.e("Switch change error for type: ${type.name}")
            }
        }
    }
}

@Keep
enum class SwitchType {
    SERVICE_IMPROVEMENT,
    VOICE_WAKEUP,
    SPEECH_RECOGNITION
}
