package com.superhexa.supervision.feature.profile.presentation.privacy

import android.os.Bundle
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.profile.R
import com.superhexa.supervision.feature.profile.databinding.FragmentPrivacySetBinding
import com.superhexa.supervision.feature.profile.presentation.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.ProductPlanning
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.language
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig.region
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.onContentClick
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.basecommon.tools.clickDebounce
import com.superhexa.supervision.library.base.domain.model.UserAction
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor
import com.superhexa.supervision.library.base.legal.LegalInfoInteractor.Companion.EXPERIENCE_IMPROVEMENT_PROGRAM
import com.superhexa.supervision.library.base.mvrx.viewbindingutils.viewBinding
import com.superhexa.supervision.library.base.presentation.fragment.InjectionFragment
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import com.superhexa.supervision.library.base.record.UserActionRecordInteractor
import com.xiaomi.aivs.config.ConfigCache
import com.xiaomi.aivs.utils.NetWorkUtil
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber

class PrivacySetFragment : InjectionFragment(R.layout.fragment_privacy_set) {
    private val viewBinding: FragmentPrivacySetBinding by viewBinding()
    private lateinit var viewModel: PrivacySetViewModel
    private val recordInteractor: UserActionRecordInteractor by instance()
    private val accountManager: AccountManager by instance()
    private val appEnvironment: AppEnvironment by instance()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewModel = ViewModelProvider(this).get(PrivacySetViewModel::class)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dealUserImprovementProgram()
    }

    @Suppress("ComplexMethod", "TooGenericExceptionCaught", "LongMethod")
    private fun dealUserImprovementProgram() {
        val deviceId = ConfigCache.authConfig?.deviceId ?: 0
        Timber.d("deviceId: $deviceId")
        viewBinding.titlebar.setOnBackClickListener {
            navigator.pop()
        }

        viewBinding.productPlanningSwitch.isChecked = MMKVUtils.decodeBoolean(
            String.format(ProductPlanning, accountManager.getUserID())
        )

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.serviceSwitchState.collect { value ->
                value?.let {
                    viewBinding.serviceImprovementSwitch.apply {
                        this.isChecked = it
                    }
                }
            }
        }

        viewBinding.productPlanningSwitch.setOnCheckedChangeListener { _, isChecked ->
            MMKVUtils.encode(String.format(ProductPlanning, accountManager.getUserID()), isChecked)
            if (isChecked) {
                val version = experienceVersion()
                val upKey = String.format(ConstsConfig.UserExperienceUpdate, region, language)
                MMKVUtils.encode(upKey, false)
                Timber.d("UALegal About Experience Record $version")
                recordInteractor.dispatchUserAction(UserAction.ConsentImprovementPlan(version))
            }
        }

        viewBinding.serviceImprovementSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateRemoteSwitchStatus(
                SwitchType.SERVICE_IMPROVEMENT,
                isChecked,
                deviceId.toString()
            )
        }
//        viewBinding.certSwitch.isChecked = MMKVUtils.decodeBoolean(TIAN_QIN_CERT_SKIP, false)
//        viewBinding.certSwitch.setOnCheckedChangeListener { _, isChecked ->
//            MMKVUtils.encode(TIAN_QIN_CERT_SKIP, isChecked)
//        }

        val stringArray = resources.getStringArray(R.array.productPlanning_clicks)
        viewBinding.tvProductPlanningTip.onContentClick(stringArray) {}
        viewBinding.tvProductPlanningTip.clickDebounce(viewLifecycleOwner) {
            val legalTerms = LegalTermsAction.LegalTerms(
                termCode = EXPERIENCE_IMPROVEMENT_PROGRAM,
                platform = LegalInfoInteractor.PLATFORM_ANDROID,
                platformVersion = appEnvironment.getAppVersion(),
                version = if (viewBinding.productPlanningSwitch.isChecked) experienceVersion() else null
            )
            Timber.d("UALegal About experienceClick $legalTerms")
            HexaRouter.Web.navigateToLegalTermsWebView(this@PrivacySetFragment, legalTerms)
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.voiceWakeUpState.collect { value ->
                value?.let {
                    viewBinding.voiceWakeUpImprovementSwitch.apply {
                        this.isChecked = it
                    }
                }
            }
        }

        viewBinding.voiceWakeUpImprovementSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (!NetWorkUtil.isNetWorkAvailable(LibBaseApplication.instance)) {
                LibBaseApplication.instance.toast(R.string.syncSwitchStatusFailed)
                viewBinding.voiceWakeUpImprovementSwitch.isChecked = !isChecked
            } else {
                viewModel.updateRemoteSwitchStatus(
                    SwitchType.VOICE_WAKEUP,
                    isChecked,
                    deviceId.toString()
                )
            }
        }

        viewLifecycleOwner.lifecycleScope.launch {
            viewModel.speechRecognitionState.collect { value ->
                value?.let {
                    viewBinding.speechRecognitionImprovementSwitch.apply {
                        this.isChecked = it
                    }
                }
            }
        }

        viewBinding.speechRecognitionImprovementSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (!NetWorkUtil.isNetWorkAvailable(LibBaseApplication.instance)) {
                LibBaseApplication.instance.toast(R.string.syncSwitchStatusFailed)
                viewBinding.speechRecognitionImprovementSwitch.isChecked = !isChecked
            } else {
                viewModel.updateRemoteSwitchStatus(
                    SwitchType.SPEECH_RECOGNITION,
                    isChecked,
                    deviceId.toString()
                )
            }
        }

        viewModel.requestRemoteSwitchStatus(deviceId.toString())
    }

    private fun experienceVersion(): String {
        val versionKey = String.format(ConstsConfig.UserExperienceVersion, region, language)
        return MMKVUtils.decodeString(versionKey) ?: ""
    }
}
