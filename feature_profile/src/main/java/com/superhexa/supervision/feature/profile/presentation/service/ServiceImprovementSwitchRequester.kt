package com.superhexa.supervision.feature.profile.presentation.service

import com.superhexa.supervision.feature.profile.data.repository.ServiceImprovementRepository
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.aivs.engine.event.DeviceConnectedEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import timber.log.Timber
import kotlin.properties.Delegates

class ServiceImprovementSwitchRequester {

    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val repository = ServiceImprovementRepository()
    private var voiceWakeUpSwitchState by Delegates.notNull<Boolean>()
    private var speechRecognitionSwitchState by Delegates.notNull<Boolean>()

    init {
        EventBus.getDefault().register(this)
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent(event: DeviceConnectedEvent) {
        requestRemoteSwitchStatus(event.deviceId.toString())
    }

    @Suppress("TooGenericExceptionCaught")
    private fun requestRemoteSwitchStatus(deviceId: String) {
        aiCapability.getToken {
            try {
                CoroutineScope(Dispatchers.IO).launch {
                    val result = repository.getServiceImprovementSwitch(deviceId, it)

                    if (result.isSuccess()) {
                        voiceWakeUpSwitchState = result.data?.data?.privacy?.voice_wakeup ?: true
                        speechRecognitionSwitchState = result.data?.data?.privacy?.speech_recognition ?: true

                        MMKVUtils.encode(
                            ConstsConfig.UserVoiceWakeUpAgreement,
                            voiceWakeUpSwitchState
                        )

                        MMKVUtils.encode(
                            ConstsConfig.UserSpeechRecognitionAgreement,
                            speechRecognitionSwitchState
                        )
                    }
                }
            } catch (e: Exception) {
                Timber.e("Improvement switch checked error")
            }
        }
    }
}
