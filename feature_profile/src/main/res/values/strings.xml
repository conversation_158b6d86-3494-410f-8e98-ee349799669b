<?xml version="1.0" encoding="utf-8"?><!DOCTYPE resources [<!ENTITY app_name "小米眼镜">]>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="signOut">Sign out</string>
    <string name="mineSetting">My</string>
    <string name="abConfigSetting">Parameter settings</string>
    <string name="removeAccountTipTitle">Cancellation notice</string>
    <string name="removeAccountApply">Apply to cancel the account</string>
    <string name="resetPassword">Reset password</string>
    <string name="videoEditor">Video editing</string>
    <string name="bleScan">Scan</string>
    <string name="removeAccountDialogTip">After you confirm to cancel your account, <PERSON><PERSON> will process your request and delete your account within 7 days. If you log in to the app again, you will be assigned a new account.</string>
    <string name="removeAccountApplyTip">To ensure that your account is secure, the following conditions must be met before your cancellation request takes effect.</string>
    <string name="removeAccountApplySaveTip">1. The account is in a secure state.</string>
    <string name="removeAccountApplySaveTipContent">The account is in normal use, no risks of theft.</string>
    <string name="removeAccountApplyPropertyTip">2. The account assets are cleared.</string>
    <string name="removeAccountApplyPropertyTipContent">No assets, arrears, outstanding funds, virtual rights and interests, and outstanding or disputed services are involved in this account or any third party accessed through this account.</string>
    <string name="removeAccountApplyManageTip">3. The account authorization is released.</string>
    <string name="removeAccountApplyManageTipContent">The account has been released from the authorized login or binding relationship with other products.</string>
    <string name="removeAccountApplyOtherTip">4. No account disputes, including complaints and reports.</string>
    <string name="removeAccountApplyPrivacy">I have read and agreed to the Xiaomi Glasses Cancellation notice</string>
    <string-array name="privacy_clicks">
        <item>@string/removeAccountPrivacy</item>
    </string-array>
    <string name="removeAccountPrivacy">Xiaomi Glasses Cancellation notice</string>
    <string name="removeAccountSure">Cancel Account</string>
    <string name="feedbackTooFewWords">Please enter at least 6 characters.</string>
    <string name="feedbackConfirm">Feedback not submitted, exit anyway?</string>
    <string name="resetGuideConfirm">Redisplaying the coach marks will restart all processes such as binding and editing. Redisplay anyway?</string>
    <string name="feedbackSuccess">Your feedback is received.</string>
    <string name="feedbackSuccessTip">提交成功！非常感谢您的反馈，这将帮助我们进一步优化体验</string>
    <string name="fileExplorer">File browsing</string>
    <string name="settingFirmwareControl">Auto Firmware Download</string>
    <string name="settingFirmwareControlTip">When the camera glasses are on, the latest firmware version will be automatically downloaded once connecting to Wi-Fi</string>
    <string name="settingGlassesStore">配镜门店信息</string>
    <string name="settingAbout">About Xiaomi Glasses</string>

    <string name="persionData">My profile</string>
    <string name="persionChangeAvatar">Change profile picture</string>
    <string name="persionNickname">Nickname</string>
    <string name="settingAccount">Account: %s</string>
    <string name="nickEditTip">Please enter a nickname</string>
    <string name="logoutService">注销服务</string>
    <string name="logoutServicePrivacy">注销协议</string>
    <string name="Next">下一步</string>
    <string name="Continue">继续</string>
    <string name="PrivacyWarn">重要提醒</string>
    <string name="PrivacyWarnContent">撤回同意后，您将退出登录状态，并且在&app_name;平台上所有数据都将被删除，无法找回。</string>
    <string name="SureCancelAgree">确认撤销</string>
    <string name="SureCancelAgreeRead">已阅读并同意《撤销隐私同意协议》</string>
    <string name="SureLogoutServiceRead">已阅读并同意《注销协议》</string>
    <string name="SureCancelAgreeReadCheck">请确认%s</string>
    <string name="SureCancelAgreeDoneToast">申请已提交，完成撤销后将退出应用</string>
    <string name="onWithdrawPrivacyPolicyConsentTitle1">撤回隐私同意须知</string>
    <string name="onWithdrawPrivacyPolicyConsentTitle2">撤回隐私同意协议</string>
    <string name="onWithdrawPrivacyPolicyConsentContent1">安全验证</string>
    <string name="onWithdrawPrivacyPolicyConsentContent2">请确保您的账号处于安全状态</string>
    <string name="onWithdrawPrivacyPolicyConsentContent3">提前保留重要信息</string>
    <string name="onWithdrawPrivacyPolicyConsentContent4">由您本人发起撤回隐私同意前，您应当自行处理相关数据，例如：将您拍摄的文件保存到系统相册。</string>
    <string name="onWithdrawPrivacyPolicyConsentContent5">您的&app_name;所有数据（设备、配置信息，以及保存在手机中的内容等）将会彻底删除且无法恢复，相机中未导出的内容也无法导出。</string>
    <string name="onWithdrawPrivacyPolicyConsentContent6">撤回本隐私同意，将不会影响您使用其他小米产品或服务。</string>
    <string name="onWithdrawPrivacyPolicyConsentContent7">撤回隐私同意过程可能需要几分钟，期间请勿再次打开应用。</string>
    <string name="onWithdrawPrivacyPolicyConsentProtocol">1.请您确保您是有权进行本撤销隐私同意操作的相关当事人。\n\n2.您的本次撤回操作，将删除该账号下&app_name; App 的所有数据，所有设备会与您当前登录的账号解除绑定，并清除设备之前存储在服务端的数据。当前手机中&app_name; App 缓存数据也将被删除。\n\n3.撤回同意还将撤销您对&app_name; App 用户协议和隐私政策的同意，并退出应用。若您要重新使用&app_name; App ，需要您再次同意&app_name; App 用户协议及隐私政策并授权相关权限。\n\n4.物理设备中存储的数据需要您进行人工本地物理重置后清除，上述数据撤销后均不可恢复。若要重新使用设备，需要您再次进行绑定并且重新授权。</string>
    <string name="SureLogoutServiceDoneToast">注销申请已提交，我们会在5天内完成&app_name;服务的注销。</string>
    <string name="logoutServiceContentTitle1">注销须知</string>
    <string name="logoutServiceContentTitle11">安全验证</string>
    <string name="logoutServiceContentTitle12">提前保留重要信息</string>
    <string name="logoutServiceContent1">请确保您的账号处于安全状态。</string>
    <string name="logoutServiceContent2">由您本人发起注销前，您应当自行处理相关数据，例如：将您拍摄的文件保存到系统相册。\n\n\n\n您的&app_name;所有数据（设备、配置信息，以及保存在手机中的内容等）将会彻底删除且无法恢复，相机中未导出的内容也无法导出。\n\n\n\n注销&app_name;，将不会影响您使用其他小米产品或服务。</string>
    <string name="logoutServiceContent21">1.请您确保您是有权进行注销操作的相关当事人。\n\n2.您的本次注销操作，将该删除账号下&app_name; App 的所有数据，所有设备会与您当前登录的账号解除绑定，并清除设备之前存储在服务端的数据。当前手机中&app_name; App 缓存数据也将被删除。\n\n3.注销还将撤销您对&app_name; App 用户协议和隐私政策的同意，并退出应用。若您要重新使用&app_name; App ，需要您再次同意&app_name; App 用户协议及隐私政策并授权相关权限。\n\n4.物理设备中存储的数据需要您前往手机系统的设置中进行人工本地物理清除，上述数据撤销后均不可恢复。若要重新使用设备，需要您再次进行绑定并且重新授权。</string>
    <string name="logoutServiceContent31">注销后，您将退出登录状态，并且在&app_name;平台上所有数据都将被删除，无法找回。</string>
    <string name="otherAccountBind">Third-party Account</string>
    <string name="resetPasswork">Change Password</string>
    <string name="removeAccount">Cancel Account</string>
    <string name="unbind">Not bound</string>
    <string name="evaluationApp">Rate Xiaomi Glasses</string>
    <string name="checkVersion">Version Detection</string>
    <string name="permissManager">System Permission</string>
    <string name="privacyManager">Privacy Policy</string>
    <string name="privacySettings">Privacy Settings</string>
    <string name="privacyTip">Legal info</string>
    <string name="cancelTheAgree">Withdraw Privacy Policy Consent</string>
    <string name="cancelTheAgreeTip">Withdraw consent</string>
    <string name="productPlanning">User Experience Improvement Program</string>
    <string name="serviceImprovement">眼镜App服务优化</string>
    <string name="serviceImprovementItemTip">开启后会在您使用眼镜App遇到问题时，上传您的日志来更好地优化服务</string>
    <string name="voiceWakeUpImprovement">语音唤醒优化</string>
    <string name="voiceWakeUpImprovementItemTip">开启后将上传唤醒音频，可支持更好地唤醒小爱</string>
    <string name="speechRecognitionImprovement">语音识别优化</string>
    <string name="speechRecognitionImprovementItemTip">开启后，会使用你的语音数据进行AI效果升级，可更好地识别您的指令</string>
    <string name="appIcpCode">APP备案编号</string>
    <string name="llmIcpCode">大模型备案</string>
    <string name="productPlanningItemTip">The User Experience Improvement Program helps to optimize product functions and experience continuously, and improve the problem-solving efficiency when users encounter usage problems.</string>
    <string name="resetApp">Redisplay the coach marks</string>
    <string name="devDeveloper" tools:ignore="MissingTranslation">Developer Options</string>
    <string name="devDeveloperMode" tools:ignore="MissingTranslation">Developer mode</string>
    <string name="devHostSwitch" tools:ignore="MissingTranslation">Switch server</string>
    <string name="feedBack">Feedback</string>
    <string name="feedBackClassify">Issue categories</string>

    <string name="feedBackSelectQuestion">Select a category</string>
    <string name="questionDesc">Issue description</string>
    <string name="questiondescCount">%s/300</string>
    <string name="questionContact">Contact (QQ/WeChat/Phone number)</string>
    <string name="roomLogTip">Upload device system logs for troubleshooting</string>
    <string name="submit">Submit</string>

    <string name="questionConnection">Connect and bind the device</string>
    <string name="questionFileTrassion">File transfer</string>
    <string name="questionLoginOrRegist">Registration and Login</string>
    <string name="questionEdite">Template</string>
    <string name="questionSetting">Device settings</string>
    <string name="questionUpdate">Firmware update</string>
    <string name="questionPhotoAndRadio">Photo-taking and recording</string>
    <string name="questionDevicePictureEffect">Device screen display effect</string>
    <string name="questionDeviceCharging">Charge</string>
    <string name="questionDeviceRecord">录音</string>
    <string name="questionWearingExperience">Wearing experience</string>
    <string name="questionSoundQuality">Sound quality</string>
    <string name="questionVolume">Volume</string>
    <string name="questionBleConnect">Bluetooth connection</string>
    <string name="questionBindDevice">Bind the device</string>
    <string name="questionTouchOperation">Touch operation</string>
    <string name="questionWearDetection">Wear detection</string>
    <string name="questionBeepFeedback">Beep feedback</string>
    <string name="questionBatteryLife">Battery life</string>

    <string name="questionTypeTip">Please select the category of the issue you encountered.</string>
    <string name="unBindTitle">Unbind %s?</string>
    <string name="unBindHint">After unbinding, you cannot log in with your %s and %s.</string>
    <string name="getRoomLogPathFailed">Couldn\'t obtain the logs, please try again.</string>
    <string name="feedbackNoConnect">Please connect the device to obtain system logs.</string>
    <string name="feedbackConnectFailedTitle">未能连接设备</string>
    <string name="feedbackConnectFailed">未能连接到眼镜设备，设备可能不在连接范围内或处于关闭状态。请确保设备已开机并在附近，重新进行反馈</string>
    <string name="feedbackConnectAndCommit">Connect device and submit</string>
    <string name="feedbackFetching">Submitting feedback…</string>
    <string name="feedbackCreatingWifi">Enabling the device hotspot…</string>
    <string name="feedbackGetingLog">正在下载设备日志，大约需要几分钟。请保持眼镜佩戴，避免摘下断连导致上传失败。</string>
    <string name="feedbackNetError">Network error, please try again.</string>

    <string name="hostUrlSwitch" tools:ignore="MissingTranslation">Please select an address to switch to.</string>
    <string name="hostOnline" tools:ignore="MissingTranslation">Online services</string>
    <string name="hostTest" tools:ignore="MissingTranslation">Testing services</string>
    <string name="hostReboot" tools:ignore="MissingTranslation">Restart to switch</string>

    <string name="permissionPermit">Allowed</string>
    <string name="permissionDisPermit">Enable</string>
    <string name="changeEmailTitle" tools:ignore="all">Email address binded</string>
    <string name="editSuccess">Reset successfully</string>
    <string name="settingQA">Help Center</string>
    <string name="o95_pair_method">准备配对</string>
    <string name="open_source_statement">Open Source Software Statement</string>
    <string name="account">账号</string>
    <string name="hostPreview">Preview</string>
    <string name="confirmLogout">您确定要退出登录前往注销账号么？</string>
    <string name="confirmLogoutBtname">确认注销</string>
    <string name="syncSwitchStatusFailed">开关状态同步失败，请重试</string>
</resources>
