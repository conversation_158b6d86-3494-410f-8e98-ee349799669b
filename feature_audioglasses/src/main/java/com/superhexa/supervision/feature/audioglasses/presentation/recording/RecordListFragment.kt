@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "TooGenericExceptionCaught", "ComplexMethod", "LargeClass")

package com.superhexa.supervision.feature.audioglasses.presentation.recording

import android.os.Bundle
import android.view.View
import androidx.activity.addCallback
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.lifecycleScope
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordShare
import com.superhexa.supervision.feature.audioglasses.presentation.router.HexaRouter
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.LONG_500
import com.superhexa.supervision.feature.audioglasses.presentation.tools.RecordingHelper.ensureUniqueNickNames
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.ss.SSstate
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ss2.RecordStateManager
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.audioglasses_RecordListFragment
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheetTitleDes2Button
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBarWithRightIcon
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.compose.component.ComposeLoading
import com.superhexa.supervision.library.base.basecommon.compose.component.EmptyScreen
import com.superhexa.supervision.library.base.basecommon.compose.component.FileActionsRow
import com.superhexa.supervision.library.base.basecommon.compose.component.HexaTabItem
import com.superhexa.supervision.library.base.basecommon.compose.component.HexaTabRow
import com.superhexa.supervision.library.base.basecommon.compose.component.HighlightText
import com.superhexa.supervision.library.base.basecommon.compose.component.RecordItem
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.extension.observeState
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color222425
import com.superhexa.supervision.library.base.basecommon.theme.Color222425_30
import com.superhexa.supervision.library.base.basecommon.theme.ColorPageBg
import com.superhexa.supervision.library.base.basecommon.theme.ColorTransparent
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_0
import com.superhexa.supervision.library.base.basecommon.theme.Dp_10
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_120
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_15
import com.superhexa.supervision.library.base.basecommon.theme.Dp_150
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_37
import com.superhexa.supervision.library.base.basecommon.theme.Dp_40
import com.superhexa.supervision.library.base.basecommon.theme.Dp_7
import com.superhexa.supervision.library.base.basecommon.theme.Dp_95
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_28
import com.superhexa.supervision.library.base.data.model.ButtonConfig
import com.superhexa.supervision.library.base.data.model.ButtonParams
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.kodein.di.generic.instance
import timber.log.Timber
import java.io.File

/**
 * 类描述:录音列表
 * 创建日期:2024/9/30
 * 作者: qiushui
 */
@Route(path = audioglasses_RecordListFragment)
class RecordListFragment : BaseComposeFragment() {
    private val viewModel by instance<RecordListViewModel>()

    override val contentView: @Composable () -> Unit = {
        val state = viewModel.mState.collectAsState()
        val tabIndex = state.value.tabIndex
        val tabItems = state.value.tabItems
        val isLoading = state.value.isLoading
        val isEditModePhone = state.value.isEditPhone
        val isEditModeGlasses = state.value.isEditGlasses
        val isExporting = state.value.isExporting
        val isSelectedPhoneFile = state.value.isSelectedPhoneFile
        val isSelectedGlassesFile = state.value.isSelectedGlassesFile
        val phoneFiles = state.value.phoneFileList
        val allFileList = state.value.allFileList
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (titleBar, tabRow, screen, tabBox) = createRefs()
            val rightIcon = if (isEditModeGlasses || isEditModePhone || isExporting) {
                R.drawable.ic_recording_white_cancel
            } else {
                R.drawable.ic_recording_file_edit
            }
            val isShowBack = isEditModeGlasses || isEditModePhone || isExporting
            CommonTitleBarWithRightIcon(
                modifier = Modifier.constrainAs(titleBar) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                },
                rightIcon = rightIcon,
                leftIcVisible = !isShowBack,
                leftIcOnClick = { navigator.pop() }
            ) {
                when (tabIndex) {
                    TAB_PHONE -> switchToEditModePhone(state)
                    TAB_GLASSES -> switchToEditModeGlasses(state)
                }
            }
            HexaTabRow(
                modifier = Modifier
                    .height(Dp_37)
                    .padding(start = Dp_20, end = Dp_95)
                    .constrainAs(tabRow) {
                        top.linkTo(titleBar.bottom, margin = Dp_20)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                containerColor = ColorTransparent,
                indicatorColor = ColorTransparent,
                containerShape = RoundedCornerShape(Dp_0),
                indicatorShape = RoundedCornerShape(Dp_0),
                selectedTabIndex = tabIndex
            ) {
                tabItems.forEachIndexed { index, title ->
                    HexaTabItem(
                        titleRes = title,
                        position = index,
                        isSelected = tabIndex == index
                    ) {
                        Timber.e("HexaTabItem $index")
                        if (getString(title).isEmpty()) {
                            Timber.d("title is empty")
                            return@HexaTabItem
                        }
                        sendEvent(RecordListUiEvent.UpdateTabIndex(index = index))
                    }
                }
            }
            if (isEditModeGlasses || isEditModePhone || isExporting) {
                val isShowButton: Boolean
                val btRes = if (tabIndex == TAB_PHONE) {
                    isShowButton = phoneFiles.isNotEmpty()
                    if (isSelectedPhoneFile) R.string.libs_cancel else R.string.allSelect
                } else {
                    isShowButton = allFileList.isNotEmpty() && !isExporting
                    if (isSelectedGlassesFile) R.string.libs_cancel else R.string.allSelect
                }
                PlaceholderTab(
                    modifier = Modifier
                        .background(ColorPageBg)
                        .constrainAs(tabBox) {
                            start.linkTo(tabRow.start, margin = Dp_28)
                            end.linkTo(tabRow.end)
                            bottom.linkTo(tabRow.bottom)
                            top.linkTo(tabRow.top)
                            width = Dimension.fillToConstraints
                        },
                    textRes = tabItems[tabIndex],
                    isShowButton = isShowButton,
                    textButton = btRes
                ) {
                    when (tabIndex) {
                        TAB_PHONE -> {
                            sendEvent(RecordListUiEvent.MultiPhoneFile(!isSelectedPhoneFile))
                        }

                        TAB_GLASSES -> {
                            sendEvent(RecordListUiEvent.MultiGlassesFile(!isSelectedGlassesFile))
                        }
                    }
                }
            }
            when (tabIndex) {
                TAB_PHONE -> {
                    PhoneScreen(
                        modifier = Modifier.constrainAs(screen) {
                            top.linkTo(tabRow.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                            height = Dimension.fillToConstraints
                        },
                        state
                    )
                }

                TAB_GLASSES -> {
                    GlassesScreen(
                        modifier = Modifier.constrainAs(screen) {
                            top.linkTo(tabRow.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                            height = Dimension.fillToConstraints
                        },
                        state
                    )
                }
            }
        }
        ComposeLoading(isLoading)
        DeleteFileDialog(state.value.isShowDeleteFile, tabIndex)
        ExportFileDialog(state.value.isShowExportFile, tabIndex)
        CancelExportDialog(state.value.isShowCancelDialog, tabIndex)
    }

    @Suppress("MagicNumber")
    @Composable
    fun PhoneScreen(modifier: Modifier = Modifier, state: State<RecordListUiState>) {
        val isEditModePhone = state.value.isEditPhone
        val isSelectedPhoneFile = state.value.isSelectedPhoneFile
        val phoneFiles = state.value.phoneFileList
        ConstraintLayout(modifier = modifier.fillMaxSize()) {
            val (tip, phone, empty, bottomBar) = createRefs()
            Timber.d("phoneFiles 页面重绘制:$phoneFiles ")
            if (phoneFiles.isEmpty()) {
                EmptyScreen(
                    messageResId = R.string.ss2RecordEmptyTip,
                    modifier = Modifier.constrainAs(empty) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = Dp_150)
                    }
                )
            }
            HighlightText(
                modifier = Modifier.constrainAs(tip) {
                    top.linkTo(parent.top, margin = Dp_20)
                    start.linkTo(parent.start, margin = Dp_12)
                    end.linkTo(parent.end, margin = Dp_12)
                    width = Dimension.fillToConstraints
                },
                text = stringResource(R.string.ss2RecordPhoneFileTip)
            )
            if (phoneFiles.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier.constrainAs(phone) {
                        top.linkTo(tip.bottom, margin = Dp_20)
                        start.linkTo(parent.start, margin = Dp_14)
                        end.linkTo(parent.end, margin = Dp_14)
                        bottom.linkTo(parent.bottom)
                        height = Dimension.fillToConstraints
                    }
                ) {
                    itemsIndexed(
                        items = phoneFiles
//                        key = { _, file -> file.fileName }
                    ) { index, file ->
                        val time = RecordingHelper.safeTimestampForDes(file.fileName)
                        val titleStr = file.nickName.ifEmpty {
                            RecordingHelper.safeTimestampForDay(file.fileName)
                        }
                        val type = RecordingHelper.getRecordingTypeString(file.recordingType)
                        val second = RecordingHelper.formatSeconds((file.duration / 1000).toInt())
                        RecordItem(
                            isEditMode = isEditModePhone,
                            isSelected = file.isSelected,
                            title = titleStr,
                            description = "$type  $time",
                            arrowDescription = second,
                            isShowRed = file.isRedPoint,
                            onLongPress = {
                                switchToEditModePhone(state)
                            },
                            onClick = {
                                // 进入录音转写、总结页面
                                HexaRouter.AudioGlasses.navigateToRecordTranscription(
                                    this@RecordListFragment,
                                    file
                                )
                            }
                        ) { selected ->
                            Timber.e("selected:$selected")
                            sendEvent(RecordListUiEvent.SelectPhoneFile(index, selected))
                        }
                    }
                    item { Box(Modifier.height(Dp_120)) {} }
                }
            }
            if (isEditModePhone) {
                FileActionsRow(
                    modifier = Modifier.constrainAs(bottomBar) {
                        bottom.linkTo(parent.bottom, margin = Dp_10)
                        start.linkTo(parent.start, margin = Dp_10)
                        end.linkTo(parent.end, margin = Dp_10)
                        width = Dimension.fillToConstraints
                    },
                    enabled = isSelectedPhoneFile,
                    exportText = stringResource(id = R.string.ss2RecordShareAudio),
                    exportIcon = R.drawable.ic_recording_white_share, // 替换为实际资源ID
                    deleteText = stringResource(id = R.string.ss2RecordDelete),
                    deleteIcon = R.drawable.ic_recording_white_delete, // 替换为实际资源ID
                    onExportClick = { shareFile() },
                    onDeleteClick = { sendEvent(RecordListUiEvent.ShowDeleteFile(true)) }
                )
            }
        }
    }

    @Composable
    fun GlassesScreen(modifier: Modifier = Modifier, state: State<RecordListUiState>) {
        val isExporting = state.value.isExporting
        val isEditModeGlasses = state.value.isEditGlasses
        val isShowEmptyScreen = state.value.isShowEmptyScreen
        val isShowNoConnectScreen = state.value.isShowNoConnectScreen
        val isSelectedGlassesFile = state.value.isSelectedGlassesFile
        val allFiles = state.value.allFileList
        ConstraintLayout(modifier = modifier.fillMaxSize()) {
            val (tip, call, empty, bottomBar, cancelBar) = createRefs()
            val usedPercentage = state.value.usedPercentage
            val availablePercentage = state.value.availablePercentage
            if (usedPercentage != null && availablePercentage != null && !isShowNoConnectScreen) {
                HighlightText(
                    modifier = Modifier.constrainAs(tip) {
                        top.linkTo(parent.top, margin = Dp_20)
                        start.linkTo(parent.start, margin = Dp_12)
                        end.linkTo(parent.end, margin = Dp_12)
                        width = Dimension.fillToConstraints
                    },
                    text = stringResource(
                        R.string.ss2RecordGlassesFileTip,
                        usedPercentage,
                        availablePercentage
                    ),
                    highlight = if (availablePercentage < SPACE_LINE) "$availablePercentage%" else null
                )
            }

            if (isShowEmptyScreen) {
                EmptyScreen(
                    messageResId = R.string.ss2RecordEmptyTip,
                    imageResId = R.drawable.ic_empty_folder,
                    modifier = Modifier.constrainAs(empty) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = Dp_150)
                    }
                )
            }
            if (isShowNoConnectScreen) {
                val resID = if (RecordStateManager.isRecording()) {
                    R.string.ss2RecordNoGetListTip
                } else {
                    R.string.ss2RecordNoConnectTip
                }
                EmptyScreen(
                    messageResId = resID,
                    imageResId = R.drawable.ic_service_error,
                    modifier = Modifier.constrainAs(empty) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top, margin = Dp_150)
                    }
                )
            }

            if (allFiles.isNotEmpty()) {
                LazyColumn(
                    modifier = Modifier.constrainAs(call) {
                        top.linkTo(tip.bottom, margin = Dp_20)
                        start.linkTo(parent.start, margin = Dp_14)
                        end.linkTo(parent.end, margin = Dp_14)
                        bottom.linkTo(parent.bottom)
                        height = Dimension.fillToConstraints
                    }
                ) {
                    itemsIndexed(items = allFiles) { index, file ->
                        val description = if (file.progress != 0F) {
                            stringResource(id = R.string.ss2RecordExportDoing)
                        } else if (file.isExported && file.isFailed) {
                            stringResource(id = R.string.ss2RecordExportFailed)
                        } else if (file.isExported) {
                            stringResource(id = R.string.ss2RecordExportWait)
                        } else {
                            val type = RecordingHelper.getRecordingTypeString(file.recordingType)
                            val time = RecordingHelper.safeTimestampForDes(file.fileName)
                            "$type $time"
                        }
                        val titleStr = RecordingHelper.safeTimestampForDay(file.fileName)
                        val duration = RecordingHelper.formatSeconds(file.duration)
                        // 如果导出失败 文案重试 否则取消
                        val errID = if (file.isFailed) R.string.libs_retry else R.string.cancel
                        RecordItem(
                            isEditMode = isEditModeGlasses,
                            isSelected = file.isSelected,
                            title = titleStr,
                            description = description,
                            arrowDescription = duration,
                            arrowDescriptionErr = stringResource(id = errID),
                            isRedNone = true,
                            isExported = file.isExported,
                            isExportedFailed = file.isFailed,
                            progress = file.progress,
                            onClick = { toast(R.string.recordTipToExp) },
                            onLongPress = {
                                switchToEditModeGlasses(state)
                            },
                            onCancelClick = {
                                if (file.isFailed) {
                                    sendEvent(RecordListUiEvent.FileExpRetry(file))
                                } else {
                                    sendEvent(RecordListUiEvent.FileExpCancelSingle(file))
                                }
                            }
                        ) { selected ->
                            Timber.e("selected:$selected")
                            sendEvent(RecordListUiEvent.SelectGlassesFile(index, selected))
                        }
                    }
                    item { Box(Modifier.height(Dp_120)) {} }
                }
            }
            if (isEditModeGlasses) {
                FileActionsRow(
                    modifier = Modifier.constrainAs(bottomBar) {
                        bottom.linkTo(parent.bottom, margin = Dp_10)
                        start.linkTo(parent.start, margin = Dp_10)
                        end.linkTo(parent.end, margin = Dp_10)
                        width = Dimension.fillToConstraints
                    },
                    enabled = isSelectedGlassesFile,
                    exportText = stringResource(id = R.string.ss2RecordExport),
                    exportIcon = R.drawable.ic_recording_white_exp,
                    deleteText = stringResource(id = R.string.ss2RecordDelete),
                    deleteIcon = R.drawable.ic_recording_white_delete,
                    onExportClick = {
                        sendEvent(RecordListUiEvent.ShowExportFile(true))
                    },
                    onDeleteClick = {
                        sendEvent(RecordListUiEvent.ShowDeleteFile(true))
                    }
                )
            }
            if (isExporting) {
                SubmitButton(
                    subTitle = stringResource(id = R.string.ss2RecordExportCancel),
                    enable = true,
                    enableColors = listOf(Color222425, Color222425),
                    disableColors = listOf(Color222425_30, Color222425_30),
                    modifier = Modifier.constrainAs(cancelBar) {
                        bottom.linkTo(parent.bottom, margin = Dp_10)
                        start.linkTo(parent.start, margin = Dp_10)
                        end.linkTo(parent.end, margin = Dp_10)
                        width = Dimension.fillToConstraints
                    }
                ) {
                    sendEvent(RecordListUiEvent.CancelExportDialog(true))
                }
            }
        }
    }

    @Composable
    private fun DeleteFileDialog(boolean: Boolean, tabIndex: Int) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordDeleteFileTip),
            des = stringResource(id = R.string.ss2RecordDeleteFileTipDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(RecordListUiEvent.ShowDeleteFile(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    sendEvent(RecordListUiEvent.ShowDeleteFile(false))
                    when (tabIndex) {
                        TAB_PHONE -> sendEvent(RecordListUiEvent.DeletePhoneFile)
                        TAB_GLASSES -> sendEvent(RecordListUiEvent.DeleteGlassesFile)
                    }
                }
            )
        )
    }

    @Composable
    private fun ExportFileDialog(boolean: Boolean, tabIndex: Int) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordExportTip),
            des = stringResource(id = R.string.ss2RecordExportTipDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(RecordListUiEvent.ShowExportFile(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    if (!viewModel.isConnected()) {
                        toast(R.string.ssDeviceNotConnected)
                        return@ButtonParams
                    }
                    if (RecordStateManager.isRecording()) {
                        toast(R.string.ss2RecordCheckTip3)
                        return@ButtonParams
                    }
                    sendEvent(RecordListUiEvent.ShowExportFile(false))
                    sendEvent(RecordListUiEvent.FileExp)
                }
            )
        )
    }

    @Composable
    private fun CancelExportDialog(boolean: Boolean, tabIndex: Int) {
        BottomSheetTitleDes2Button(
            title = stringResource(id = R.string.ss2RecordTipExport),
            des = stringResource(id = R.string.ss2RecordTipExportDes),
            visible = boolean,
            buttonConfig = ButtonConfig.TwoButton(
                ButtonParams(text = stringResource(id = R.string.cancel)) {
                    sendEvent(RecordListUiEvent.CancelExportDialog(false))
                },
                ButtonParams(text = stringResource(id = R.string.sure)) {
                    sendEvent(RecordListUiEvent.CancelExportDialog(false))
                    sendEvent(RecordListUiEvent.FileExpCancelAll)
                }
            )
        )
    }

    @Composable
    fun PlaceholderTab(
        modifier: Modifier,
        textRes: Int,
        isShowButton: Boolean,
        textButton: Int,
        onMultiClick: () -> Unit
    ) {
        Row(
            modifier = modifier
                .fillMaxWidth()
                .height(Dp_37)
                .clickable(false) { },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.weight(1f),
                text = stringResource(id = textRes),
                color = ColorWhite,
                fontSize = Sp_28,
                fontWeight = FontWeight.W600,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            if (isShowButton) {
                MultiButton(
                    subTitle = stringResource(id = textButton),
                    modifier = Modifier.padding(end = Dp_28),
                    onClick = onMultiClick
                )
            }
        }
    }

    @Composable
    fun MultiButton(
        subTitle: String,
        modifier: Modifier,
        enable: Boolean = true,
        onClick: () -> Unit
    ) {
        Box(
            modifier = modifier
                .wrapContentWidth()
                .wrapContentHeight()
                .clip(RoundedCornerShape(Dp_40))
                .background(color = Color222425)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = rememberRipple(),
                    enabled = enable,
                    onClick = { onClick.invoke() }
                )
        ) {
            Text(
                text = subTitle,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(horizontal = Dp_15, vertical = Dp_7),
                style = TextStyle(
                    color = if (enable) ColorWhite else ColorWhite30,
                    fontSize = Sp_13,
                    fontFamily = FontFamily.SansSerif,
                    textAlign = TextAlign.Center
                )
            )
        }
    }

    private fun shareFile() = lifecycleScope.launch(Dispatchers.IO) {
        val selectedFiles = viewModel.mState.value.phoneFileList.filter { it.isSelected }
        sendEvent(RecordListUiEvent.Loading(true))
        try {
            // 根据文件数量，执行不同的逻辑
            if (selectedFiles.size == 1) {
                // 单文件分享
                val file = selectedFiles[0]
                RecordShare.share(requireContext(), file.fileDnPath, file.nickName)
            } else {
                // 多文件压缩后分享
                val ensureUniqueNickNames = ensureUniqueNickNames(selectedFiles.toMutableList())
                val mp3FilePaths = ensureUniqueNickNames.mapNotNull {
                    if (File(it.fileUpPath).exists()) {
                        File(it.fileUpPath).absolutePath
                    } else {
                        File(it.fileDnPath).absolutePath
                    }
                }
                val zipFilePath = RecordingHelper.zipMultipleFiles(mp3FilePaths)
                if (zipFilePath.isNotEmpty()) {
                    Timber.d("文件成功压缩到: $zipFilePath")
                    RecordingHelper.shareSingleFile(requireContext(), File(zipFilePath))
                } else {
                    Timber.e("压缩文件失败。")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "分享异常")
        } finally {
            sendEvent(RecordListUiEvent.EditPhone(false))
            delay(LONG_500)
            sendEvent(RecordListUiEvent.Loading(false))
        }
    }

    private fun switchToEditModePhone(state: State<RecordListUiState>) {
        sendEvent(RecordListUiEvent.EditPhone(!state.value.isEditPhone))
    }

    private fun switchToEditModeGlasses(state: State<RecordListUiState>) {
        if (state.value.isExporting) {
            sendEvent(RecordListUiEvent.CancelExportDialog(true))
        } else {
            sendEvent(RecordListUiEvent.EditGlasses(!state.value.isEditGlasses))
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initObserver()
        checkBackPressed()
        val pageFrom = arguments?.getString(BundleKey.RECORD_EXP_PAGE_FROM) ?: ""
        val pageTab = arguments?.getString(BundleKey.RECORD_EXP_PAGE_TAB) ?: ""
        if (pageTab.isNotEmpty()) { // 只有管理历史文件仅展示手机储蓄的tab 其他情况两个tab均展示
            sendEvent(RecordListUiEvent.SyncTabItems(historyList()))
        } else {
            sendEvent(RecordListUiEvent.SyncTabItems())
        }
        if (pageFrom.isNotEmpty()) {
            sendEvent(RecordListUiEvent.UpdateTabIndex(index = 1))
        }
        getPhoneFiles()
    }

    override fun onResume() {
        super.onResume()
        getPhoneFiles()
    }

    private fun getPhoneFiles() {
        val tabIndex = viewModel.mState.value.tabIndex
        Timber.e("getPhoneFiles called tabIndex:$tabIndex")
        if (tabIndex == 0) {
            viewModel.sendEvent(RecordListUiEvent.GetPhoneFiles)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Timber.e("RecordListFragment onDestroyView")
    }

    private fun initObserver() {
        viewModel.decorator.liveData.runCatching {
            observeState(viewLifecycleOwner, SSstate::deviceState) { state ->
                if (state == DeviceState.Disconnected && viewModel.mState.value.isExporting) {
                    toast(R.string.bluetoothNotConnected)
                }
            }
        }
    }

    private fun sendEvent(action: RecordListUiEvent) {
        viewModel.sendEvent(action)
    }

    private fun checkBackPressed() {
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner) {
            if (!viewModel.mState.value.isExporting) {
                navigator.pop()
            }
        }
    }
}
