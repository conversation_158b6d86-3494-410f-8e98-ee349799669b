@file:Suppress("MagicNumber")

package com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.component

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import com.superhexa.supervision.feature.audioglasses.R
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordOptionResult
import com.superhexa.supervision.feature.audioglasses.presentation.recording.transcription.RecordTranscriptionViewModel
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.Dp_1
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_13
import com.superhexa.supervision.library.base.basecommon.theme.Dp_14
import com.superhexa.supervision.library.base.basecommon.theme.Dp_16
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_4
import com.superhexa.supervision.library.base.basecommon.theme.Dp_6
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_20
import timber.log.Timber
import kotlin.math.pow

/**
 * <AUTHOR>
 * @date 2025/7/8 14:00.
 * description：总结页面
 */
@Suppress("LongParameterList")
@Composable
fun ConstraintLayoutScope.RecordSummaryScreen(
    viewModel: RecordTranscriptionViewModel,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    loadingRef: ConstrainedLayoutReference, // loading的refId
    reSummaryRef: ConstrainedLayoutReference // 重新总结对应的refId
) {
    val recordResult = viewModel.recordResultLiveData.observeAsState()
    val recordOptionsValue = recordResult.value
    val tab = viewModel.tabLiveData.value
    Timber.d("RecordSummaryScreen called tab:$tab, recordResult:$recordResult")
    if (tab == RecordTranscriptionViewModel.Tab.Transcribe) {
        return
    }

    when (recordOptionsValue) {
        is RecordOptionResult.LoadingOption -> {
            val summaryLoading = recordOptionsValue.summaryLoading
            if (summaryLoading) {
                RecordLoading(
                    refId = loadingRef,
                    topRef = topRef,
                    bottomRef = bottomRef,
                    isSummary = true
                )
            } else {
                // 没有loading就展示总结内容
                ShowSummary(
                    viewModel = viewModel,
                    topRef = topRef,
                    bottomRef = bottomRef,
                    loadingRef = loadingRef,
                    reSummaryRef = reSummaryRef
                )
            }
        }

        else -> {
            // 显示总结内容
            ShowSummary(
                viewModel = viewModel,
                topRef = topRef,
                bottomRef = bottomRef,
                loadingRef = loadingRef,
                reSummaryRef = reSummaryRef
            )
        }
    }
}

@Suppress("LongParameterList")
@Composable
private fun ConstraintLayoutScope.ShowSummary(
    viewModel: RecordTranscriptionViewModel,
    topRef: ConstrainedLayoutReference,
    bottomRef: ConstrainedLayoutReference,
    loadingRef: ConstrainedLayoutReference,
    reSummaryRef: ConstrainedLayoutReference,
    template: String = viewModel.summaryTemplate
) {
    val summaryTitle = viewModel.transcriptionSummaryTitle.value
    val summaryContent = viewModel.transcribeSummary.value
    Timber.i("showSummary title:${summaryTitle.isNotEmpty()}, content:${summaryContent.isNotEmpty()}")
    if (summaryTitle.isNotEmpty() && summaryContent.isNotEmpty()) {
        Column(
            modifier = Modifier.constrainAs(reSummaryRef) {
                start.linkTo(parent.start)
                top.linkTo(topRef.bottom)
                end.linkTo(parent.end)
                bottom.linkTo(bottomRef.top, margin = Dp_13)
                height = Dimension.fillToConstraints
            }
        ) {
            ReSummaryTips(viewModel)
            Column(
                modifier = Modifier.verticalScroll(rememberScrollState())
            ) {
                ReSummaryTitle(viewModel, template)
                SummaryContent(viewModel)
            }
        }
    } else {
        RecordLoading(
            refId = loadingRef,
            topRef = topRef,
            bottomRef = bottomRef,
            true
        )
    }
}

@Composable
private fun ReSummaryTips(
    viewModel: RecordTranscriptionViewModel
) {
    val alphaAnimate = remember { Animatable(0f) }
    val alphaState = viewModel.alphaState
    LaunchedEffect(Unit) {
        if (alphaState.value) {
            alphaAnimate.animateTo(
                targetValue = 1f,
                animationSpec = tween(durationMillis = 1000)
            )
            alphaState.value = false
        }
    }
    val alpha by alphaAnimate.asState()

    Column(
        modifier = Modifier
            .padding(all = Dp_20)
            .alpha(alpha = if (!alphaState.value) 1f else alpha)
    ) {
        Row(
            modifier = Modifier
                .background(
                    color = Color18191A,
                    shape = RoundedCornerShape(Dp_12)
                )
                .fillMaxWidth()
                .wrapContentHeight()
                .clickable { viewModel.showReSummarizeDialog(true) },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                stringResource(R.string.text_retry_summary_change),
                fontSize = Sp_13,
                fontWeight = FontWeight.W400,
                color = ColorWhite,
                modifier = Modifier
                    .padding(all = Dp_14)
                    .weight(1f)
            )
            Image(
                painter = painterResource(R.drawable.icon_summary_right_arrow),
                contentDescription = "right arrow",
                modifier = Modifier.padding(end = Dp_14)
            )
        }
    }
}

@Composable
private fun ReSummaryTitle(
    viewModel: RecordTranscriptionViewModel,
    template: String
) {
    val stateValue = viewModel.mState.collectAsState()
    val bean = stateValue.value.currentItem
    val templateItem = getTemplateItemByValue(template.ifEmpty { "abstractAutopilot" })
    bean?.let {
        Column(
            modifier = Modifier
                .padding(start = Dp_20, end = Dp_20)
        ) {
            Text(
                viewModel.transcriptionSummaryTitle.value,
                fontSize = Sp_20,
                fontWeight = FontWeight.W500,
                color = ColorWhite
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(vertical = Dp_16),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .background(
                            color = templateItem.tabBgColor,
                            shape = RoundedCornerShape(Dp_4)
                        )
                        .wrapContentHeight()
                ) {
                    Text(
                        templateItem.displayName,
                        fontSize = Sp_13,
                        fontWeight = FontWeight.W400,
                        color = templateItem.tagColor,
                        modifier = Modifier.padding(horizontal = Dp_6, vertical = Dp_4)
                    )
                }

                Spacer(Modifier.width(Dp_16))
                Text(
                    DateTimeUtils.formatDuration(bean.duration),
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    color = Color.White,
                    modifier = Modifier.alpha(0.6F)
                )
                Spacer(Modifier.width(Dp_16))
                Text(
                    text = DateTimeUtils.convertTimeStampToDateString(fileCreateTime(bean.fileName)),
                    fontSize = Sp_13,
                    fontWeight = FontWeight.W400,
                    color = Color.White,
                    modifier = Modifier.alpha(0.6F)
                )
            }
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(Dp_1)
                    .background(color = Color18191A)
                    .padding(vertical = Dp_16)
            )
        }
    }
}

@Suppress("MagicNumber")
private fun fileCreateTime(fileName: String): Long {
    Timber.d("fileCreateTime called => $fileName")
    val timestamp = fileName.toLongOrNull() ?: 0L
    return if (timestamp < 1_000_000_000_000L) { // 小于13位，补足为13位
        timestamp * 10.0.pow(13 - timestamp.toString().length).toLong()
    } else {
        timestamp
    }
}

@Composable
private fun SummaryContent(
    viewModel: RecordTranscriptionViewModel
) {
    val summaryContent = viewModel.transcribeSummary.value
    Text(
        text = summaryContent,
        modifier = Modifier.padding(start = Dp_20, end = Dp_20),
        fontSize = Sp_16,
        fontWeight = FontWeight.W400,
        color = Color.White
    )
}
