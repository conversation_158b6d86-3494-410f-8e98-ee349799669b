package com.superhexa.supervision.feature.xiaoai.presentation.observer

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95State
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.aivs.MiWearAivsHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.decorator.IDeviceOperator
import com.superhexa.supervision.feature.xiaoai.R
import com.superhexa.supervision.feature.xiaoai.track.EventTrackHelper
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.extension.StateTuple1
import com.superhexa.supervision.library.base.basecommon.extension.observeStateWithReturn
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.db.bean.bluedevice.BondDevice
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogState
import com.xiaomi.aivs.data.InstructionConst
import com.xiaomi.aivs.engine.event.DeviceConnectedEvent
import com.xiaomi.aivs.monitor.MonitorId
import com.xiaomi.aivs.track.EventTrackKv
import com.xiaomi.wear.protobuf.nano.SystemProtos
import com.xiaomi.wear.protobuf.nano.SystemProtos.AlertStatus.Electrochromic
import com.xiaomi.wearable.context
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

class DeviceObserver {

//    private var timerPolicy = BtTimerPolicy()

    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val miWearAivsHandler by lazy { MiWearAivsHandler }
    private val decorator
        get() = if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }

    private var deviceState: LiveData<StateTuple1<DeviceState>>? = null
    private var deviceInfo: LiveData<StateTuple1<SystemProtos.DeviceInfo?>>? = null
    private var electrochemical: LiveData<StateTuple1<Electrochromic?>>? = null
    private var electricity: LiveData<StateTuple1<SystemProtos.DeviceStatus?>>? = null

    // TODO 目前只支持同时一台可用设备,切换后更新设备.
    fun initObserver(
        lifecycleOwner: LifecycleOwner,
        onDeviceConnectState: (Boolean, BondDevice?) -> Unit
    ): BondDevice? {
        Timber.d("initObserver")
        return if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            initDeviceInfo(decorator?.liveData?.value?.deviceInfo)
            addDeviceObserve(decorator, lifecycleOwner, onDeviceConnectState)
            bondDevice
        } else {
            null
        }
    }

    fun isDeviceConnected() = decorator?.isChannelSuccess() == true

    private fun initDeviceInfo(deviceInfo: SystemProtos.DeviceInfo?) {
        Timber.d("initDeviceInfo:$deviceInfo")
        val firmwareVersion = deviceInfo?.firmwareVersion ?: ""
        EventTrackHelper.appendCommonParams(
            EventTrackKv.ROM_VER.point(),
            firmwareVersion
        )
        MMKVUtils.encode(ConstsConfig.FIRMWARE_VERSION, firmwareVersion)
        bondDevice?.let {
            EventTrackHelper.appendCommonParams(EventTrackKv.DEVICE_ID.point(), "${it.deviceId}")
        }
    }

    private fun addDeviceObserve(
        decorator: IDeviceOperator<O95StateLiveData>?,
        lifecycleOwner: LifecycleOwner,
        onDeviceConnectState: (Boolean, BondDevice?) -> Unit
    ) {
        Timber.d("addDeviceObserve")
        deviceState?.removeObservers(lifecycleOwner)
        electrochemical?.removeObservers(lifecycleOwner)
        deviceInfo?.removeObservers(lifecycleOwner)
        electricity?.removeObservers(lifecycleOwner)

        decorator?.liveData?.runCatching {
            deviceState = observeStateWithReturn(lifecycleOwner, O95State::deviceState) { state ->
                Timber.d("observerDeviceState deviceState:$state,$this")
                if (decorator.isChannelSuccess()) {
//                    timerPolicy.cancelTimer("decorator ChannelSuccess")
                    onDeviceConnectState.invoke(true, bondDevice)
                    var lastDeviceId = MMKVUtils.decodeLong(ConstsConfig.LAST_DEVICE_ID)
                    if (lastDeviceId != bondDevice?.deviceId) {
                        EventBus.getDefault().post(DeviceConnectedEvent(bondDevice?.deviceId))
                        MMKVUtils.encode(ConstsConfig.LAST_DEVICE_ID, bondDevice?.deviceId)
                    }
                } else {
                    if (state == DeviceState.Disconnected) {
                        onDeviceConnectState.invoke(false, bondDevice)
                        if (MiWearAivsHandler.getLastVoiceStatus() == DialogState.RECORDING) {
                            miWearAivsHandler.sendAivsErrorExitInstruction(
                                InstructionConst.SysExceptionCode.EXIT_WITH_DEVICE_DISCONNECTED,
                                context.getString(R.string.tip_exit_with_device_disconnected)
                            )
                        }
                    }
//                    timerPolicy.restartTimer(LibBaseApplication.instance, "deviceState:$state")
                    val dialogState = AiSpeechEngine.INSTANCE.dialogState()
                    Timber.d("observerDeviceState dialogState:$dialogState")
                    if (dialogState != DialogState.VOICE_IDLE) {
                        AiSpeechEngine.INSTANCE.finishSession(
                            reason = "deviceState:$state",
                            playSound = false
                        )
                    }
                }
            }

            electrochemical = observeStateWithReturn(lifecycleOwner, O95State::electrochromic) {
                Timber.d("observerDevice electrochemical:$it,$this")
                AiSpeechEngine.INSTANCE.onMonitorEvent(MonitorId.WEARABLE)
            }

            deviceInfo = observeStateWithReturn(lifecycleOwner, O95State::deviceInfo) {
                Timber.d("observerDevice deviceInfo:$it,$this")
                initDeviceInfo(it)
            }

            electricity = observeStateWithReturn(lifecycleOwner, O95State::deviceStatus) {
                Timber.d("observerDevice electricity:${it?.battery},$this")
                AiSpeechEngine.INSTANCE.onMonitorEvent(MonitorId.WEARABLE)
            }
        }
    }
}
