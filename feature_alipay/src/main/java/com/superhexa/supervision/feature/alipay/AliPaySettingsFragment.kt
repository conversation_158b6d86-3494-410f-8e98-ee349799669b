package com.superhexa.supervision.feature.alipay

import android.os.Bundle
import android.view.View
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.ViewModelProvider
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.superhexa.supervision.feature.alipay.model.AliPayGuideViewModel
import com.superhexa.supervision.feature.alipay.router.HexaRouter
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.compose.CommonTitleBar
import com.superhexa.supervision.library.base.basecommon.compose.TitleArrowDes
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color18191A
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite
import com.superhexa.supervision.library.base.basecommon.theme.ColorWhite60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_30
import com.superhexa.supervision.library.base.basecommon.theme.Dp_33
import com.superhexa.supervision.library.base.basecommon.theme.Dp_50
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_13
import com.superhexa.supervision.library.base.basecommon.theme.Sp_17
import com.superhexa.supervision.library.base.basecommon.tools.ImmersiveManager
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.superhexa.supervision.library.base.presentation.views.LegalTermsAction
import timber.log.Timber

@Suppress("TooManyFunctions", "LargeClass")
@Route(path = RouterKey.ALIPAY_SETTINGS_FRAGMENT)
class AliPaySettingsFragment : BaseComposeFragment() {
    private val faqUrl =
        "https://watch.iot.mi.com/html/earphone_faq/index.html?locale=zh_cn&model=miwear.phovideo.o95cn#/UserGuide/ScanPayWithAlipay.md"
    private val viewModel: AliPayGuideViewModel by lazy {
        ViewModelProvider(
            this,
            ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
        )[AliPayGuideViewModel::class.java]
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.addRecordObserver(this)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.removeRecordStateObserver()
    }

    override val contentView: @Composable () -> Unit = {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .then(if (ImmersiveManager.isImmersive.value) Modifier.statusBarsPadding() else Modifier)
        ) {
            val (titleBar, funs, action) = createRefs()
            CommonTitleBar(
                getString(R.string.libs_alipay_settings),
                modifier = Modifier.constrainAs(titleBar) { top.linkTo(parent.top) }
            ) { navigator.pop() }
            FunList(
                modifier = Modifier.constrainAs(funs) {
                    top.linkTo(titleBar.bottom)
                    bottom.linkTo(action.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                    height = Dimension.fillToConstraints
                }
            )
            BottomAction(
                modifier = Modifier.constrainAs(action) {
                    bottom.linkTo(parent.bottom, margin = Dp_30)
                    top.linkTo(funs.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }
            )
        }
    }

    @Suppress("TooManyFunctions")
    @Composable
    private fun FunList(
        modifier: Modifier
    ) {
        LazyColumn(
            modifier = modifier
                .fillMaxWidth()
        ) {
            item {
                OtherItem()
            }
        }
    }

    @Composable
    private fun PaySettingsItem() {
        com.superhexa.supervision.feature.alipay.component.VerticalSpaced(height = Dp_28)
        SettingItemDesc(tipResource = R.string.tips_alipay_settings)
        // ====== 免密支付额度 ======
        TitleArrowDes(
            title = stringResource(id = R.string.tips_alipay_free),
            description = null,
            enabled = true,
            arrowDescription = ""
        ) {
            // 点击跳转逻辑待定
        }
        // ====== 支付顺序 ======
        TitleArrowDes(
            title = stringResource(id = R.string.tips_alipay_order),
            enabled = true,
            arrowDescription = ""
        ) {
            // 点击跳转逻辑待定
        }
    }

    @Composable
    private fun OtherItem() {
        com.superhexa.supervision.feature.alipay.component.VerticalSpaced(height = Dp_28)
        //    SettingItemDesc(tipResource = R.string.tips_alipay_other)
        // ====== 新手引导 ======
        TitleArrowDes(
            title = stringResource(id = R.string.tips_alipay_new_task),
            description = null,
            enabled = true,
            arrowDescription = ""
        ) {
            Timber.i("navigateToAliPayGuide")
            if (!viewModel.getConnectStates() || !viewModel.getWearStates()) {
                requireContext().toast("请佩戴眼镜并保证蓝牙连接")
                return@TitleArrowDes
            }
            HexaRouter.Alipay.navigateToAliPayGuide(this)
        }
        // ====== 常见问题 ======
        TitleArrowDes(
            title = stringResource(id = R.string.tips_alipay_settings_question),
            enabled = true,
            arrowDescription = ""
        ) {
            HexaRouter.Alipay.navigateToLegalTermsWebView(
                fragment = this,
                terms = LegalTermsAction.Permalink(faqUrl)
            )
        }
    }

    @Composable
    fun SettingItemDesc(tipResource: Int) {
        Text(
            text = stringResource(id = tipResource),
            style = TextStyle(
                color = ColorWhite60,
                fontSize = Sp_13,
                fontWeight = FontWeight.W300,
                lineHeight = Sp_17
            ),
            modifier = Modifier
                .height(Dp_33)
                .padding(start = Dp_20, top = Dp_8)
        )
    }

    @Composable
    private fun BottomAction(
        modifier: Modifier
    ) {
        val brush = Brush.horizontalGradient(listOf(Color18191A, Color18191A))
        com.superhexa.supervision.feature.alipay.component.AliPayGradientButton(
            modifier = modifier
                .fillMaxWidth()
                .height(Dp_50)
                .padding(horizontal = Dp_30),
            brush,
            ColorWhite,
            textRes = R.string.text_alipay_unbinding,
            onConfirm = {
                AlipaySDKManager.INSTANCE.unBind()
            }
        )
    }
}
