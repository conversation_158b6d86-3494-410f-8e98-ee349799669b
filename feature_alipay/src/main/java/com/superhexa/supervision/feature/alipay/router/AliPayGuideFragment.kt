@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "LongMethod")

package com.superhexa.supervision.feature.alipay.router

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstrainedLayoutReference
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintLayoutScope
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.alibaba.android.arouter.facade.annotation.Route
import com.github.fragivity.applySlideInOut
import com.github.fragivity.navigator
import com.github.fragivity.pop
import com.github.fragivity.push
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.supervision.feature.alipay.AliPayGuideDialog
import com.superhexa.supervision.feature.alipay.AlipaySDKManager
import com.superhexa.supervision.feature.alipay.R
import com.superhexa.supervision.feature.alipay.data.GuideState
import com.superhexa.supervision.feature.alipay.data.PageContent
import com.superhexa.supervision.feature.alipay.model.AliPayGuideViewModel
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.ALIPAY_GUIDE_FINISH
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey.ALIPAY_POINTS_PAGE_FIRST
import com.superhexa.supervision.library.base.basecommon.extension.clickDebounce
import com.superhexa.supervision.library.base.basecommon.extension.safeActivity
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack
import com.superhexa.supervision.library.base.basecommon.theme.ColorBlack70
import com.superhexa.supervision.library.base.basecommon.theme.Dp_12
import com.superhexa.supervision.library.base.basecommon.theme.Dp_150
import com.superhexa.supervision.library.base.basecommon.theme.Dp_20
import com.superhexa.supervision.library.base.basecommon.theme.Dp_210
import com.superhexa.supervision.library.base.basecommon.theme.Dp_28
import com.superhexa.supervision.library.base.basecommon.theme.Dp_32
import com.superhexa.supervision.library.base.basecommon.theme.Dp_45
import com.superhexa.supervision.library.base.basecommon.theme.Dp_60
import com.superhexa.supervision.library.base.basecommon.theme.Dp_8
import com.superhexa.supervision.library.base.basecommon.theme.Sp_15
import com.superhexa.supervision.library.base.basecommon.theme.Sp_16
import com.superhexa.supervision.library.base.basecommon.theme.Sp_24
import com.superhexa.supervision.library.base.basecommon.theme.Sp_30
import com.superhexa.supervision.library.base.basecommon.theme.Sp_45
import com.superhexa.supervision.library.base.basecommon.tools.ImmersiveManager
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil.clearTransparent
import com.superhexa.supervision.library.base.basecommon.tools.StatusBarUtil.setTransparent
import com.superhexa.supervision.library.base.presentation.fragment.BaseComposeFragment
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.listener.IAlipayListener
import com.xiaomi.aivs.engine.listener.IAlipayResultListener
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl.Companion.TtsStopOptions
import com.xiaomi.aivs.player.UtteranceListener
import timber.log.Timber

@Route(path = ALIPAY_POINTS_PAGE_FIRST)
class AliPayGuideFragment :
    BaseComposeFragment(),
    IAlipayResultListener,
    UtteranceListener,
    IAlipayListener {
    private val viewModel: AliPayGuideViewModel by lazy {
        ViewModelProvider(
            this,
            ViewModelProvider.AndroidViewModelFactory(requireActivity().application)
        )[AliPayGuideViewModel::class.java]
    }
    private lateinit var lifecycleOwner: LifecycleOwner

    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy {
        if (DeviceModelManager.isMijiaO95SeriesDevice(bondDevice?.model)) {
            DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice)
        } else {
            null
        }
    }

    @SuppressLint("ResourceAsColor")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Timber.i("onViewCreated")
        lifecycleOwner = viewLifecycleOwner
        AiSpeechEngine.INSTANCE.addAlipayObserver(TAG, lifecycleOwner?.lifecycle, this)
        viewModel.initSteps()
        immersiveStatusBar()
        AiSpeechEngine.INSTANCE.addAlipayResultsObserver(
            TAG,
            lifecycleOwner?.lifecycle,
            this
        )
        AiSpeechEngine.INSTANCE.enterAlipayDemoMode()

        // 观察导航事件
        viewModel.navigateToFinish.observe(
            lifecycleOwner,
            Observer { event ->
                Timber.i("navigateToFinish")
                event.getContentIfNotHandled()?.let {
                    navigator.pop()
                    navigator.push(
                        ARouterTools.navigateToFragment(ALIPAY_GUIDE_FINISH)::class
                    ) {
                        applySlideInOut()
                    }
                }
            }
        )

        // 观察对话框事件
        viewModel.showDialogEvent.observe(
            lifecycleOwner,
            Observer { event ->
                event.getContentIfNotHandled()?.let { message ->
                    AlipaySDKManager.INSTANCE.alipayExi("devices disconnect")
                    val dialog = AliPayGuideDialog()
                    dialog.setOnConfirmAction {
                        confirm(dialog)
                    }
                    dialog.setOnCancelAction {
                        viewModel.updateDialogStatus(false)
                        navigator.pop()
                    }
                    activity?.let {
                        dialog.show(it.supportFragmentManager, "AliPayGuideDialog")
                        viewModel.updateDialogStatus(true)
                    }
                }
            }
        )
    }

    private fun confirm(dialog: AliPayGuideDialog) {
        if (!viewModel.getConnectStates() || !viewModel.getWearStates()) {
            requireContext().toast(R.string.text_alipay_connect_tips)
            return
        }
        viewModel.restartGuide("dialog show")
        viewModel.updateDialogStatus(false)
        dialog.dismiss()
    }

    @SuppressLint("ImplicitSamInstance")
    override fun onDestroyView() {
        super.onDestroyView()
        Timber.i("onDestroyView")
        AiSpeechEngine.INSTANCE.stopTts(
            stopOptions = TtsStopOptions(
                calledFrom = "onDestroyView",
                needResumeMediaPlayer = AiSpeechEngine.INSTANCE.isLongAudioPlaying(),
                needStopMediaPlayer = null,
                stopReason = null
            )
        )
        AiSpeechEngine.INSTANCE.removeAlipayObserver(TAG)
    }

    private fun immersiveStatusBar() {
        safeActivity()?.apply {
            ImmersiveManager.setImmersive(true)
            setTransparent(this)
        }
    }

    private fun recoverStatusBar() {
        safeActivity()?.apply {
            clearTransparent(this, R.color.black)
            ImmersiveManager.setImmersive(false)
        }
    }

    override fun onResume() {
        super.onResume()
        Timber.i("onResume")
        viewModel.addRecordObserver(this)
        AiSpeechEngine.INSTANCE.enterAlipayDemoMode()
    }

    override fun onPause() {
        super.onPause()
        Timber.i("onPause")
    }

    override fun onStop() {
        super.onStop()
        Timber.i("onStop")
        AiSpeechEngine.INSTANCE.exitAlipayDemoMode()
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.i("onDestroy")
        AiSpeechEngine.INSTANCE.removeAlipayResultsObserver(TAG)
        AiSpeechEngine.INSTANCE.exitAlipayDemoMode()
        viewModel.removeRecordStateObserver()
        recoverStatusBar()
    }

    @SuppressLint("ResourceAsColor")
    override val contentView: @Composable () -> Unit = {
        val isImmersive = rememberIsImmersive()

        // 从 ViewModel 获取页面内容
        val pages = viewModel.pages
        val currentIndex by viewModel.currentIndex.collectAsState()

        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (bg, titleBar) = createRefs()
            AiSpeechPageBg(
                sourceId = if (currentIndex == 2) {
                    R.mipmap.ic_guide_ai_speech_item_alipay_ambiguous
                } else {
                    R.mipmap.ic_guide_ai_speech_item_alipay
                },
                bg
            )
            AliPayGuideTitleBar(
                modifier = Modifier.constrainAs(titleBar) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                },
                isImmersive = isImmersive,
                onBack = {
                    navigator.pop()
                }
            )
            MultiLayoutSlideAnimation(
                pages = pages,
                currentIndex = currentIndex
            )
        }
    }

    @OptIn(ExperimentalAnimationApi::class)
    @Composable
    fun MultiLayoutSlideAnimation(
        pages: List<PageContent>,
        currentIndex: Int
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxSize()
        ) {
            val (transBg) = createRefs()
            Box(
                modifier = Modifier
                    .constrainAs(transBg) {
                        bottom.linkTo(parent.bottom)
                    }
                    .fillMaxWidth()
                    .height(50.dp)
                    .background(
                        Brush.verticalGradient(
                            colorStops = arrayOf(
                                0.0f to Color.Black.copy(alpha = 0f),
                                0.5f to Color.Black.copy(alpha = 0.5f),
                                1.0f to Color.Black
                            )
                        )
                    )
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(16.dp),
                contentAlignment = Alignment.Center
            ) {
                AnimatedContent(
                    targetState = currentIndex,
                    transitionSpec = {
                        slideInVertically(initialOffsetY = { -it }) + fadeIn() with
                            slideOutVertically(targetOffsetY = { it }) + fadeOut()
                    },
                    label = "Layout Slide Animation"
                ) { index ->
                    val page = pages[index]
                    PageLayout(
                        title = page.title,
                        mainText = page.mainText,
                        subTitle = page.subTitle,
                        imageId = page.imageId
                    )
                }
            }
        }
    }

    @Composable
    internal fun ConstraintLayoutScope.AiSpeechPageBg(
        @DrawableRes sourceId: Int = 0,
        bg: ConstrainedLayoutReference
    ) {
        if (sourceId != 0) {
            Image(
                modifier = Modifier
                    .constrainAs(bg) {
                        top.linkTo(parent.top)
                    }
                    .fillMaxWidth(),
                painter = painterResource(id = sourceId),
                contentDescription = "",
                contentScale = ContentScale.FillWidth
            )
        }
    }

    // 单个页面的布局结构
    @SuppressLint("LongMethod")
    @Composable
    fun PageLayout(title: String?, mainText: String, subTitle: String, imageId: Int?) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            val (littleTitle, titleDesc, description, image) = createRefs()
            if (title != null) {
                Text(
                    text = title,
                    modifier = Modifier
                        .constrainAs(littleTitle) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            bottom.linkTo(titleDesc.top, margin = Dp_12)
                            width = Dimension.fillToConstraints
                        }
                        .width(Dp_210),
                    style = TextStyle(
                        color = ColorBlack,
                        fontSize = Sp_16,
                        fontWeight = FontWeight.W400,
                        lineHeight = Sp_24,
                        textAlign = TextAlign.Center
                    )
                )
            }
            Text(
                text = mainText,
                modifier = Modifier
                    .constrainAs(titleDesc) {
                        centerHorizontallyTo(parent)
                        top.linkTo(parent.top, margin = Dp_150)
                        bottom.linkTo(description.top, margin = Dp_8)
                    }
                    .padding(horizontal = Dp_60),
                style = TextStyle(
                    color = ColorBlack,
                    fontSize = Sp_30,
                    fontWeight = FontWeight.W400,
                    lineHeight = Sp_45,
                    textAlign = TextAlign.Center
                ),
                maxLines = 2
            )
            Text(
                text = subTitle,
                modifier = Modifier
                    .constrainAs(description) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(titleDesc.bottom, margin = Dp_12)
                        width = Dimension.fillToConstraints
                    }
                    .padding(horizontal = Dp_28),
                style = TextStyle(
                    color = ColorBlack70,
                    fontSize = Sp_15,
                    fontWeight = FontWeight.Light,
                    lineHeight = Sp_24,
                    textAlign = TextAlign.Center
                )
            )
            if (imageId != null) {
                Image(
                    painter = painterResource(id = imageId),
                    contentDescription = "code",
                    modifier = Modifier
                        .constrainAs(image) {
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                            top.linkTo(description.bottom, margin = Dp_45)
                        }
                        .padding(horizontal = Dp_28)
                )
            }
        }
    }

    @Composable
    fun AliPayGuideTitleBar(
        modifier: Modifier,
        isImmersive: Boolean = false,
        onBack: () -> Unit
    ) {
        ConstraintLayout(
            modifier = modifier
                .then(if (isImmersive) Modifier.statusBarsPadding() else Modifier)
                .fillMaxWidth()
                .heightIn(min = Dp_32)
                .padding(horizontal = Dp_20)
        ) {
            val (back) = createRefs()
            Image(
                painter = painterResource(id = R.drawable.ic_alipay_guide_close),
                contentDescription = "",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .constrainAs(back) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        bottom.linkTo(parent.bottom)
                    }
                    .size(Dp_28)
                    .clickDebounce { onBack.invoke() }
            )
        }
    }

    @Composable
    fun rememberIsImmersive(): Boolean {
        return ImmersiveManager.isImmersive.value
    }

    override fun scanPayResult(code: Int, msg: String?) {
        Timber.tag(TAG).d("scanPayResult:$code $msg")
        if (viewModel.guideState.value is GuideState.actionStartScan) {
            if (code == 0) {
                viewModel.updateSteps(GuideState.actionScanSucess)
            } else {
                viewModel.restartGuide("scanPayResult $code")
            }
        }
    }

    override fun onPayResult(code: String?, msg: String?) {
        Timber.tag(TAG).d("onPayResult:$code $msg")
        if (code == "9000") {
            viewModel.updateSteps(GuideState.Finished)
        } else {
            viewModel.restartGuide("onPayResult $msg")
        }
    }

    // region 常量定义
    companion object {
        private const val TAG = "AliPayGuideFragment"
    }

    override fun offlineCloudStop(startTime: Long) {
    }

    override fun updateDialogId(dialogId: String?) {
    }

    override fun scanPay(sessionId: String?, dialogId: String, asrResult: String) {
    }

    override fun scanPayWithDemoMode(sessionId: String?, dialogId: String, asrResult: String) {
    }

    override fun alipayExi(reason: String?) {
    }

    override fun alipayInterrupt(reason: String?) {
    }

    override fun sendVoiceData(bytes: ByteArray) {
    }

    override fun startFirstVoice() {
        Timber.i("startFirstVoice")
        if (viewModel.guideState.value is GuideState.actionStartVoice) {
            viewModel.updateSteps(GuideState.actionStartScan)
        }
    }

    override fun clearVoiceData() {
    }
}
