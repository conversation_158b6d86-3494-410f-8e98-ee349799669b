# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in D:\SDK\SDK/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

#-injars      bin/classes
#-injars      libs
#-outjars     bin/classes-processed.jar
#-libraryjars    'D:\SDK\SDK\platforms\android-23\android.jar'
-keepclassmembers class com.superhexa.supervision.library.base.data.config.BuildConfig {
     public static final java.lang.String BASE_URL;
}
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontoptimize
-dontpreverify
-verbose
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

-keepattributes *Annotation*
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
#七牛存储
#-keep class com.qiniu.**{*;}
#-keep class com.qiniu.**{public <init>();}

#是否忽略警告级别的错误，如果不设置，会导致有警告混淆通不过
-ignorewarnings

#极光推送
#-dontwarn com.google.**
#-keep class com.google.gson.jpush.** { *;}

#所有实体类
-keep class android.content.* {*;}
-dontwarn android.content.**

#okhttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase


#okio  和 retrofit    https://github.com/square/okhttp/issues/964
-dontwarn okio.**
-keep class okio.** { *;}
-dontwarn javax.annotation.**


#okio     https://github.com/square/okhttp/issues/964
-dontwarn -dontwarn rx.internal.util.**
-keep class rx.** { *;}


#Lamda表达式
-dontwarn java.lang.invoke.*
-dontwarn **$$Lambda$*

#BRVAH库
-keep class com.chad.library.adapter.** {
*;
}
-keep public class * extends com.chad.library.adapter.base.BaseQuickAdapter
-keep public class * extends com.chad.library.adapter.base.BaseViewHolder
-keepclassmembers public class * extends com.chad.library.adapter.base.BaseViewHolder {
     <init>(android.view.View);
}

# for RxJava:
-dontwarn sun.misc.Unsafe


# for EventBus3:


-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}


## Only required if you use AsyncExecutor
#-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
#    <init>(java.lang.Throwable);
#}



# for JS 调用

-keepattributes *Annotation*
-keepattributes *JavascriptInterface*

-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}



#微信分享
-dontwarn com.tencent.**
-keep class com.tencent.**{*;}
#-keep class com.tencent.mm.sdk.modelmsg.WXMediaMessage { *;}
#-keep class com.tencent.mm.sdk.modelmsg.** implements com.tencent.mm.sdk.modelmsg.WXMediaMessage$IMediaObject {*;}
-keep class com.tencent.mm.opensdk.modelmsg.WXMediaMessage { *;}
-keep class com.tencent.mm.opensdk.modelmsg.** implements com.tencent.mm.opensdk.modelmsg.WXMediaMessage$IMediaObject {*;}

#微信SDK
#-keep class com.tencent.mm.sdk.** {
#   *;
#}

-keep class com.tencent.mm.opensdk.** {
   *;
}

#友盟统计
-keep class com.umeng.** {*;}
-keep class com.umeng.commonsdk.** {*;}


#Joup
#-keep class org.jsoup.**  { *;}				#离线下载中用到的，替换img ，javascipt ，css 标签包的类

#ZXing
#-dontwarn com.google.zxing.**
#-keep class com.google.zxin.**{*;}



# Only required if you use AsyncExecutor
#-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
#  <init>(java.lang.Throwable);
#}


#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;   public *;
}
  # for DexGuard only
#  -keep resourcexmlelements manifest/application/meta-data@value=GlideModule


-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}

#腾讯bugly
-dontwarn com.tencent.bugly.**
-keep public class com.tencent.bugly.**{*;}
-keep class android.support.**{*;}
-dontwarn com.tencent.bugly.**

#android-gif-drawable  gif加载库
-keep class pl.droidsonroids.gif.**{*;}
-keep public class pl.droidsonroids.gif.GifIOException{<init>(int, java.lang.String);}
-keep public class pl.droidsonroids.gif.GifIOException{<init>(int);}
-keep class pl.droidsonroids.gif.GifInfoHandle{<init>(long,int,int,int);}


#aSwipeLayout 库
-keep class com.daimajia.swipe.**{*;}

#阿里云推送
-keepclasseswithmembernames class ** {
    native <methods>;
}
-keepattributes Signature
-keep class sun.misc.Unsafe { *; }
-keep class com.taobao.** {*;}
-keep class com.alibaba.** {*;}
-keep class com.alipay.** {*;}
-dontwarn com.taobao.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-keep class com.ut.** {*;}
-dontwarn com.ut.**
-keep class com.ta.** {*;}
-dontwarn com.ta.**
-keep class anet.**{*;}
-keep class org.android.spdy.**{*;}
-keep class org.android.agoo.**{*;}
-dontwarn anet.**
-dontwarn org.android.spdy.**
-dontwarn org.android.agoo.**

#阿里云推送 第三方通道混淆设置
# 小米通道
-keep class com.xiaomi.** {*;}
-dontwarn com.xiaomi.**
# 华为通道
-keep class com.huawei.** {*;}
-dontwarn com.huawei.**
# GCM/FCM通道 Google设置 暂时没用到
#-keep class com.google.firebase.**{*;}
#-dontwarn com.google.firebase.**



-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Optimization is turned off by default. Dex does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
-dontoptimize
-dontpreverify
# Note that if you want to enable optimization, you cannot just
# include optimization flags in your own project configuration file;
# instead you will need to point to the
# "proguard-android-optimize.txt" file instead of this one from your
# project.properties file.



#-keepattributes *Annotation*
#-keep public class com.google.vending.licensing.ILicensingService
#-keep public class com.android.vending.licensing.ILicensingService

# For native methods, see http://proguard.sourceforge.net/manual/examples.html#native
#-keepclasseswithmembernames class * {
#    native <methods>;
#}

# keep setters in Views so that animations can still work.
# see http://proguard.sourceforge.net/manual/examples.html#beans
-keepclassmembers public class * extends android.view.View {
   void set*(***);
   *** get*();
}

# We want to keep methods in Activity that could be used in the XML attribute onClick
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# For enumeration classes, see http://proguard.sourceforge.net/manual/examples.html#enumerations
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepclassmembers class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator CREATOR;
}

-keepclassmembers class **.R$* {
    public static <fields>;
}

# The support library contains references to newer platform versions.
# Don't warn about those in case this app is linking against an older
# platform version.  We know about them, and they are safe.
-dontwarn android.support.**




-dontoptimize
-dontpreverify

#-dontwarn cn.jpush.**
#-keep class cn.jpush.** { *; }






# Jackson

-keepattributes *Annotation*,EnclosingMethod,Signature
-keepnames class com.fasterxml.jackson.** { *; }
-dontwarn com.fasterxml.jackson.databind.**


-keep @com.fasterxml.jackson.annotation.JsonIgnoreProperties class * { *; }
-keep @com.fasterxml.jackson.annotation.JsonCreator class * { *; }
-keep @com.fasterxml.jackson.annotation.JsonValue class * { *; }
-keep class com.fasterxml.** { *; }
-keep class org.codehaus.** { *; }
-keepnames class com.fasterxml.jackson.** { *; }
-keepclassmembers public final enum com.fasterxml.jackson.annotation.JsonAutoDetect$Visibility {
    public static final com.fasterxml.jackson.annotation.JsonAutoDetect$Visibility *;
}

# General
-keepattributes SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,Signature,Exceptions,InnerClasses




#-keep class org.codehaus.** { *; }
#-keepclassmembers public final enum org.codehaus.jackson.annotate.JsonAutoDetect$Visibility {
#  public static final org.codehaus.jackson.annotate.JsonAutoDetect$Visibility *;
# }
#-keep public class your.class.** {
#  public void set*(***);
#  public *** get*();
#}


-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions


-keepattributes InnerClasses


-printmapping build/outputs/mapping/release/mapping.txt


##友盟混淆设置
-dontshrink
#-keep class * extends com.umeng.socialize.net.base.SocializeReseponse {
#   *;
#}
-dontoptimize
-dontwarn com.google.android.maps.**
-dontwarn android.webkit.WebView
-dontwarn com.umeng.**
-dontwarn com.tencent.weibo.sdk.**
-dontwarn com.facebook.**
-keep public class javax.**
-keep public class android.webkit.**
-dontwarn android.support.v4.**
-keep enum com.facebook.**
-keepattributes Exceptions,InnerClasses,Signature
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable

-keep public interface com.facebook.**
-keep public interface com.tencent.**
-keep public interface com.umeng.socialize.**
-keep public interface com.umeng.socialize.sensor.**
-keep public interface com.umeng.scrshot.**

-keep public class com.umeng.socialize.* {*;}


-keep class com.facebook.**
-keep class com.facebook.** { *; }
-keep class com.umeng.scrshot.**
-keep public class com.tencent.** {*;}
-keep class com.umeng.socialize.sensor.**
-keep class com.umeng.socialize.handler.**
-keep class com.umeng.socialize.handler.*
-keep class com.umeng.weixin.handler.**
-keep class com.umeng.weixin.handler.*
-keep class com.umeng.qq.handler.**
-keep class com.umeng.qq.handler.*
-keep class UMMoreHandler{*;}
-keep class com.tencent.mm.sdk.modelmsg.WXMediaMessage {*;}
-keep class com.tencent.mm.sdk.modelmsg.** implements com.tencent.mm.sdk.modelmsg.WXMediaMessage$IMediaObject {*;}
-keep class im.yixin.sdk.api.YXMessage {*;}
-keep class im.yixin.sdk.api.** implements im.yixin.sdk.api.YXMessage$YXMessageData{*;}
-keep class com.tencent.mm.sdk.** {
   *;
}
-keep class com.tencent.mm.opensdk.** {
   *;
}
-keep class com.tencent.wxop.** {
   *;
}
-keep class com.tencent.mm.sdk.** {
   *;
}

-keep class com.twitter.** { *; }
-dontwarn twitter4j.**
-keep class twitter4j.** { *; }

-keep class com.tencent.** {*;}
-dontwarn com.tencent.**
-keep class com.kakao.** {*;}
-dontwarn com.kakao.**
-keep public class com.umeng.com.umeng.soexample.R$*{
    public static final int *;
}
-keep public class com.linkedin.android.mobilesdk.R$*{
    public static final int *;
}
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class com.tencent.open.TDialog$*
-keep class com.tencent.open.TDialog$* {*;}
-keep class com.tencent.open.PKDialog
-keep class com.tencent.open.PKDialog {*;}
-keep class com.tencent.open.PKDialog$*
-keep class com.tencent.open.PKDialog$* {*;}
-keep class com.umeng.socialize.impl.ImageImpl {*;}
-keep class com.sina.** {*;}
-dontwarn com.sina.**
-keep class  com.alipay.share.sdk.** {
   *;
}

-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

-keep class com.linkedin.** { *; }
-keep class com.android.dingtalk.share.ddsharemodule.** { *; }
-keepattributes Signature

#permissions4m 权限申请
-dontwarn com.joker.api.**
-keep class com.joker.api.** {*;}
-keep interface com.joker.api.** { *; }
-keep class **$$PermissionsProxy { *; }


#融云推送==============================================================
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-keep public class com.google.firebase.* {*;}
-dontnote io.rong.**
-keep class com.lizihang.powerapp.receivers.RongImNotificationReceiver {*;}

-keepattributes Exceptions,InnerClasses

-keepattributes Signature

# RongCloud SDK
-keep class io.rong.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

# VoIP
-keep class io.agora.rtc.** {*;}

# Location
-keep class com.amap.api.**{*;}
-keep class com.amap.api.services.**{*;}

# 红包
-keep class com.google.gson.** { *; }
-keep class com.uuhelper.Application.** {*;}
-keep class net.sourceforge.zbar.** { *; }
-keep class com.google.android.gms.** { *; }
-keep class com.alipay.** {*;}
-keep class com.jrmf360.rylib.** {*;}

-ignorewarnings
#融云推送==============================================================


#=====华为推送==============================
-keepattributes *Annotation*
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable
-keep class com.hianalytics.android.**{*;}
-keep class com.huawei.updatesdk.**{*;}
-keep class com.huawei.hms.**{*;}
-keep class com.huawei.android.hms.agent.**{*;}
-keep class com.huawei.gamebox.plugin.gameservice.**{*;}

-keep class com.huawei.android.pushagent.**{*;}
-keep class com.huawei.android.pushselfshow.**{*;}
-keep class com.huawei.android.microkernel.**{*;}
-keep class com.baidu.mapapi.**{*;}



#=====高德地图===============================
#3D 地图 V5.0.0之前：
-keep   class com.amap.api.maps.**{*;}
-keep   class com.autonavi.amap.mapcore.*{*;}
-keep   class com.amap.api.trace.**{*;}
##跟随融云升级后升级高德需要加入下面折行
-keep class com.mapbox.mapboxsdk.**{ *; }

#融云语音CallKit
-keep class cn.rongcloud.rtc.core.**  { *; }
-keep class cn.rongcloud.rtc.engine.binstack.json.**  { *; }
#融云语音CallLib
-keep class com.blink.**  { *; }
-keep class com.bailingcloud.bailingvideo.engine.binstack.json.**  { *; }
-keep class bailingquic.**{*;}
-keep class go.**{*;}
#融云语音RongRTCLib 开始
-keepattributes Exceptions,InnerClasses

-keepattributes Signature
#RongRTCLib
-keep public class cn.rongcloud.** {*;}

#RongIMLib
-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

-ignorewarnings
#融云语音RongRTCLib 结束

#3D 地图 V5.0.0之后：
-keep   class com.amap.api.maps.**{*;}
-keep   class com.autonavi.**{*;}
-keep   class com.amap.api.trace.**{*;}

#定位
-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}

#搜索
-keep   class com.amap.api.services.**{*;}

#2D地图
-keep class com.amap.api.maps2d.**{*;}
-keep class com.amap.api.mapcore2d.**{*;}

#导航
-keep class com.amap.api.navi.**{*;}
-keep class com.autonavi.**{*;}

 #百度地图
-keep class com.baidu.** {*;}
-keep class mapsdkvi.com.** {*;}
-dontwarn com.baidu.**

# GSYVideoPlayer
-keep class com.shuyu.gsyvideoplayer.video.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.**
-keep class com.shuyu.gsyvideoplayer.video.base.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.video.base.**
-keep class com.shuyu.gsyvideoplayer.utils.** { *; }
-dontwarn com.shuyu.gsyvideoplayer.utils.**
-keep class tv.danmaku.ijk.** { *; }
-dontwarn tv.danmaku.ijk.**

-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, java.lang.Boolean);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# 用于 实体类避免混淆的配置，实体类加@keep即可避免混淆
-keepattributes *Annotation*
-keep @android.support.annotation.Keep class *{*;}

-keep public class * extends com.superhexa.bean.Base {*;}


# 迁移androidX后 keep注解 包名变了

-dontskipnonpubliclibraryclassmembers
-printconfiguration
-keep,allowobfuscation @interface androidx.annotation.Keep

-keep @androidx.annotation.Keep class *{*;}
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}


# 协程混淆设置

# ServiceLoader support
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcherFactory {}

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# Same story for the standard library's SafeContinuation that also uses AtomicReferenceFieldUpdater
-keepclassmembernames class kotlin.coroutines.SafeContinuation {
    volatile <fields>;
}


# 对如下问题的支持 对jackson解析kotlin中data 类中某些字段名称 形似isShowStrategy 中驼峰命名和返回方法形同的情况  字段解析不出来
# https://github.com/FasterXML/jackson-module-kotlin
-keep class kotlin.Metadata { *; }
-keep class kotlin.reflect.** { *; }

#腾讯云视频
-keep class com.tencent.** { *; }


# 对如下问题的支持 对jackson解析kotlin中data 类中某些字段名称 形似isShowStrategy 中驼峰命名和返回方法形同的情况  字段解析不出来
# https://github.com/FasterXML/jackson-module-kotlin
-keep class kotlin.Metadata { *; }
-keep class kotlin.reflect.** { *; }


## TabLayout被混淆
-keep class com.google.android.** {*;}
-keep class android.support.design.widget.TabLayout{*;}

## 升级完androidX 后，androidX混淆
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-dontwarn com.google.android.material.**
-dontwarn androidx.**

#阿里热修复sophix
#基线包使用，生成mapping.txt
-printmapping mapping.txt
#生成的mapping.txt在app/build/outputs/mapping/release路径下，移动到/app路径下
#修复后的项目使用，保证混淆结果一致
#-applymapping mapping.txt
#hotfix
-keep class com.taobao.sophix.**{*;}
-keep class com.ta.utdid2.device.**{*;}
-dontwarn com.alibaba.sdk.android.utils.**
#防止inline
-dontoptimize

-keepclassmembers class com.superhexa {
    public <init>();
}
-keep class com.superhexa.supervision.app.SuperVisionApplication$RealApplicationStub

# BasePlayer  基础播放器
-keep public class * extends android.view.View{*;}

-keep public class * implements com.kk.taurus.playerbase.player.IPlayer{*;}

# BaseRecyclerViewAdapterHelper
-keepclassmembers class fqcn.of.javascript.interface.for.webview {
   public *;
}

-keep class com.chad.library.adapter.** {
*;
}
-keep public class * extends com.chad.library.adapter.base.BaseQuickAdapter
-keep public class * extends com.chad.library.adapter.base.viewholder.BaseViewHolder
-keepclassmembers  class **$** extends com.chad.library.adapter.base.viewholder.BaseViewHolder {
     <init>(...);
}
-keepattributes InnerClasses

-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-dontwarn androidx.**


-keep public class cn.jzvd.JZMediaSystem {*; }
-keep public class cn.jzvd.demo.CustomMedia.CustomMedia {*; }
-keep public class cn.jzvd.demo.CustomMedia.JZMediaIjk {*; }
-keep public class cn.jzvd.demo.CustomMedia.JZMediaSystemAssertFolder {*; }

-keep class tv.danmaku.ijk.media.player.** {*; }
-dontwarn tv.danmaku.ijk.media.player.*
-keep interface tv.danmaku.ijk.media.player.** { *; }


#aliyunplayer
-keep class com.alivc.**{*;}
-keep class com.aliyun.**{*;}
-keep class com.cicada.**{*;}
-dontwarn com.alivc.**
-dontwarn com.aliyun.**
-dontwarn com.cicada.**

# 日志文件框架
-keep class ch.qos.** { *; }
-keep class org.slf4j.** { *; }
-keepattributes *Annotation*
-keepclassmembers class ch.qos.logback.classic.pattern.* { <init>(); }
-keep class com.superhexa.supervision.library.base.log.** { *; }
-dontwarn com.superhexa.supervision.library.base.log.**


# Arouter
-keep public class com.alibaba.android.arouter.routes.**{*;}
-keep public class com.alibaba.android.arouter.facade.**{*;}
-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}
-keep public class * implements com.alibaba.android.arouter.facade.template.ISyringe { *; }
-keep @com.alibaba.android.arouter.facade.annotation.Route class * { *; }


# If you use the byType method to obtain Service, add the following rules to protect the interface:
-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider

# If single-type injection is used, that is, no interface is defined to implement IProvider,
#the following rules need to be added to protect the implementation
-keep class * implements com.alibaba.android.arouter.facade.template.IProvider


# 锐动sdk
-dontwarn com.vecore.**
-keep class com.vecore.** { *; }

# 小米sdk
-dontwarn com.xiaomi.**
-keep class com.xiaomi.** { *; }
# 小米账号sdk 5.0.0版  参考https://xiaomi.f.mioffice.cn/wiki/wikk4Cg5Vtzzo8bte4hofJ08P2g#  混淆
-keep,includedescriptorclasses class net.sqlcipher.** { *; }
-keep,includedescriptorclasses interface net.sqlcipher.** { *; }
-keep,includedescriptorclasses class com.xiaomi.account.** { *; }
-keep,includedescriptorclasses interface com.xiaomi.account.** { *; }
-keep,includedescriptorclasses class com.xiaomi.accounts.** { *; }
-keep,includedescriptorclasses interface com.xiaomi.accounts.** { *; }
-keep,includedescriptorclasses class com.xiaomi.accountsdk.** { *; }
-keep,includedescriptorclasses interface com.xiaomi.accountsdk.** { *; }
-keep,includedescriptorclasses class com.xiaomi.passport.** { *; }
-keep,includedescriptorclasses interface com.xiaomi.passport.** { *; }



# Jetpack  startup
-keep class androidx.startup.AppInitializer
-keep class * extends androidx.startup.Initializer

# 依赖注入 kodein
-dontwarn org.kodein.**
-keep class org.kodein.** { *; }

# 不混淆所有实现KodeinModuleProvider的类
-keep public interface com.superhexa.supervision.library.base.di.KodeinModuleProvider { *;}
-keep class * implements com.superhexa.supervision.library.base.di.KodeinModuleProvider {
<methods>;
<fields>;
}

-keep public interface org.kodein.di.KodeinAware { *;}
-keep class * implements org.kodein.di.KodeinAware {
<methods>;
<fields>;
}

# fragivity
-keep class com.github.fragivity.** { *; }
-dontwarn com.github.fragivity.**

# viewBinding
-keep,allowoptimization class * implements androidx.viewbinding.ViewBinding {
    public static *** bind(android.view.View);
    public static *** inflate(android.view.LayoutInflater);
}

#-keepclassmembers class * extends androidx.viewbinding.ViewBinding {
#    public static *** bind(android.view.View);
#}
#-keepclassmembers class com.wonderquill.databinding.**  {
#    public <methods>;
#}
#
#-keep class * implements androidx.viewbinding.ViewBinding {
#    public static *** bind(android.view.View);
#    public static *** inflate(android.view.LayoutInflater);
#}


# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Keep annotation default values (e.g., retrofit2.http.Field.encoded).
-keepattributes AnnotationDefault

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items).
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response

# With R8 full mode generic signatures are stripped for classes that are not
# kept. Suspend functions are wrapped in continuations where the type argument
# is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

# google 自己的库
-dontwarn com.google.**
-keep class com.google.** { *; }

# 加载视图库， 由于我们是源码引入，所以直接在下面过滤 https://github.com/HarlonWang/AVLoadingIndicatorView
-keep class com.superhexa.supervision.library.base.customviews.loading.** { *; }

# 蓝牙扫描sdk
-dontwarn no.nordicsemi.**
-keep class no.nordicsemi.** { *; }
-keep class com.superhexa.lib.channel.data.** { *; }

# xiaomi passportsdk
-keep class net.sqlcipher.** { *; }
-keep class net.sqlcipher.database.* { *; }
# rxjava
-dontwarn java.util.concurrent.Flow*
# immersionBar 沉浸式库
-keep class com.gyf.immersionbar.* {*;}
-dontwarn com.gyf.immersionbar.**

# iflytek讯飞语音合成
-dontwarn com.iflytek.**
-keep class com.iflytek.** { *; }
-keepattributes Signature

# 拼音库
-keep class net.sourceforge.pinyin4j.** {*;}

-keep class com.superhexa.supervision.library.mipush.MiPushMessageReceiver {*;}

# MPAndroidChart
-dontwarn com.github.mikephil.**
-keep class com.github.mikephil.**{ *; }


-keep class com.google.android.filament.** { *; }

# sceneview 3d 模型加载库
-keep class com.google.android.filament.** { *; }
-keep class io.github.sceneview.** {*;}
-keep class dev.romainguy.** {*;}
-dontwarn io.github.sceneview.**

# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep the annotations that proguard needs to process.
-keep class com.google.android.filament.proguard.UsedBy*

# Just because native code accesses members of a class, does not mean that the
# class itself needs to be annotated - only annotate classes that are
# referenced themselves in native code.
-keep @com.google.android.filament.proguard.UsedBy* class * {
  <init>();
}
-keepclassmembers class * {
  @com.google.android.filament.proguard.UsedBy* *;
}

# These classes is loaded via env->FindClass() from Utils.cpp
# They are in the utils namespace and therefore not covered by previous rules.
-keep class com.google.android.filament.utils.KTX1Loader
-keep class com.google.android.filament.utils.HDRLoader

# These native JNI methods are loaded via env->RegisterNatives() from Utils.cpp
-keepclassmembers class com.google.android.filament.utils.KTX1Loader {
   native <methods>;
}
-keepclassmembers class com.google.android.filament.utils.HDRLoader {
   native <methods>;
}

# mmkv 混淆规则
-keep class com.tencent.mmkv.** { *; }

# ---------- mis 混淆-----------
# netty
-keep class io.netty.** {*;}
# mdns
-keep class com.xiaomi.mis.system_wrapper.MdnsLib {*;}
-keep class com.xiaomi.mis.system_wrapper.MdnsLib$* {*;}
# ---------- mis 混淆-----------
-keep class com.xiaomi.ai.** { *; }
# 添加 aivs-sdk gson beans 混淆
-keep public class com.xiaomi.ai.android.netbeans.** { *; }

# ai-api-spec 协议类，不能混淆
-keep class com.xiaomi.ai.api.** { *; }
-keep class com.xiaomi.ai.core.** { *; }
-keep public class com.xiaomi.ai.core.XMDChannel{*;}

# FFmpegMediaMetadataRetriever 防混淆规则
-keep class wseemann.media.** { *; }
-keep class com.github.wseemann.** { *; }
-keepclasseswithmembernames class wseemann.media.FFmpegMediaMetadataRetriever {
    native <methods>;
}
#喜马拉雅SDK确保签名验证正确
-dontwarn com.ximalaya.ting.android.host.service.xmcontrolapi.**
-keep class com.ximalaya.ting.android.host.service.xmcontrolapi.**{*;}