@file:Suppress("EmptyFunctionBlock")

package com.superhexa.supervision.app

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.os.LocaleList
import android.util.Log
import androidx.work.Configuration
import com.superhexa.supervision.BuildConfig
import com.superhexa.supervision.BuildConfig.DEBUG
import com.superhexa.supervision.app.feature.FeatureManager
import com.superhexa.supervision.appModule
import com.superhexa.supervision.feature.alipay.AlipaySDKManager
import com.superhexa.supervision.feature.miwear.speechhub.service.RecordAudioService
import com.superhexa.supervision.feature.miwear.speechhub.service.TranscriptionNotificationManager
import com.superhexa.supervision.feature.profile.presentation.service.ServiceImprovementSwitchRequester
import com.superhexa.supervision.library.base.baseModule
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.log.FileLogTree
import com.superhexa.supervision.library.base.log.ReleaseLogTree
import com.superhexa.supervision.startup.AppInitializer.TAG
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.kodein.di.Kodein
import org.kodein.di.KodeinAware
import org.kodein.di.android.x.androidXModule
import org.kodein.di.generic.bind
import org.kodein.di.generic.singleton
import timber.log.Timber
import java.util.Locale

/**
 * SuperVisionApplication 集成libBase中的LibBaseApplication，方便其他子模块能使用application

 */
@Suppress("TooGenericExceptionCaught")
class SuperVisionApplication : LibBaseApplication(), KodeinAware, Configuration.Provider {
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private var activityCount = 0
    private var isInForeground = false
    private lateinit var serviceImprovementSwitchRequester: ServiceImprovementSwitchRequester

    @SuppressLint("LogNotTimber")
    override val kodein = Kodein.lazy {
        if (isMainProcess()) {
            bind<Context>() with singleton { this@SuperVisionApplication }
            import(androidXModule(instance))
            import(baseModule)
            import(appModule)
            importAll(FeatureManager.kodeinModules)
            Timber.tag(TAG).d("Kodein import finished.")
        } else {
            Log.i(TAG, "Skipping Kodein initialization in non-main process.")
            Kodein {} // 非主进程返回一个空的 Kodein 容器，避免 NPE
        }
    }

    /**
     * workManager 移除默认配置的那种每次启动初始化，manifest中有对应配置
     * 使用时按需初始化，提高启动速度
     *
     * 官方文档参见
     * https://developer.android.com/topic/libraries/architecture/workmanager/
     * advanced/custom-configuration#on-demand
     * @return Configuration
     */
    override fun getWorkManagerConfiguration() = Configuration.Builder()
        .setMinimumLoggingLevel(Log.INFO)
        .build()

    override fun attachBaseContext(base: Context?) {
        if (BuildConfig.FLAVOR.contains(ConstsConfig.FlavorGlobal) || base == null) {
            super.attachBaseContext(base)
        } else {
            val configuration = base.resources.configuration
            configuration.setLocale(Locale.SIMPLIFIED_CHINESE)
            val localeList = LocaleList(Locale.SIMPLIFIED_CHINESE)
            configuration?.setLocales(localeList)
            LocaleList.setDefault(localeList)
            super.attachBaseContext(base.createConfigurationContext(configuration))
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    @SuppressLint("LogNotTimber")
    override fun onCreate() {
        super.onCreate()
        serviceImprovementSwitchRequester = ServiceImprovementSwitchRequester()
        if (!isMainProcess()) {
            initOtherLogBackProperty(this)
            initTimber()
            Log.i(TAG, "Skipping Application onCreate in non-main process.")
            return
        }
//        AlertStatusNotificationManager.createNotificationChannel(this)
        // 初始化转写通知管理器
        TranscriptionNotificationManager.getInstance()

        // 恢复未完成的转写任务
        GlobalScope.launch {
            try {
                RecordAudioService.getInstance().recoverUncompletedTranscriptions()
                Timber.tag(TAG).d("转写任务恢复完成")
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "恢复转写任务时发生错误")
            }
        }
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

            override fun onActivityStarted(activity: Activity) {
                activityCount++
                if (!isInForeground && activityCount > 0) {
                    isInForeground = true
                    // 应用进入前台
                    onAppForegrounded()
                }
            }

            override fun onActivityResumed(activity: Activity) {}

            override fun onActivityPaused(activity: Activity) {}

            override fun onActivityStopped(activity: Activity) {
                activityCount--
                if (isInForeground && activityCount === 0) {
                    isInForeground = false
                    // 应用退到后台
                    onAppBackgrounded()
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}

            override fun onActivityDestroyed(activity: Activity) {}
        })

        Timber.tag(TAG).d("Application onCreate in main process.")
    }

    private fun initTimber() {
        if (DEBUG) {
            Timber.plant(Timber.DebugTree(), FileLogTree())
        } else {
            Timber.plant(ReleaseLogTree(), FileLogTree())
        }
        Timber.tag(TAG).d("Application initTimber")
    }

    private fun initOtherLogBackProperty(context: Context) {
        System.setProperty("LOG_FILE_NAME", "log_processor.roll.log")
        System.setProperty("LOG_DIR", context.getFilesDir().getAbsolutePath())
    }

    /**
     * 判断当前进程是否是主进程
     */
    private fun isMainProcess(): Boolean {
        val processName = getProcessName() ?: return false
        return processName == packageName
    }

    override fun onTerminate() {
        super.onTerminate()
        Timber.d("onTerminate")
        applicationScope.cancel()
    }

    private fun onAppForegrounded() {
        // 处理应用回到前台的逻辑
        Timber.d("onAppForegrounded,应用进入前台")
        applicationScope.launch(Dispatchers.Main) {
            AlipaySDKManager.INSTANCE.getBindStatus()
        }
    }

    private fun onAppBackgrounded() {
        // 处理应用退到后台的逻辑
        Timber.d("onAppBackgrounded,应用退到后台")
    }
}
