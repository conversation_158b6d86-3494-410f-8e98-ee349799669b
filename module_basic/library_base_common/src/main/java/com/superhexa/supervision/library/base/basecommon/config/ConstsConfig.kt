package com.superhexa.supervision.library.base.basecommon.config

object ConstsConfig {
    //    const val AutoScanCheckFlag: String = "AutoScanCheckFlag"
    const val AppConfigKey: String = "AppConfig"
    const val AccountConfigKey: String = "Accountconfig"
    const val CookiesKey: String = "Cookies"
    const val SeviceToken: String = "f7b364516e739c2ac60f1114e88f6afc"
    const val LogOutUrlKey: String = "LogOutUrl_%s"
    const val ProductPlanningKey: String = "ProductPlanning_%s"
    const val fileAuthoritySuffix: String = ".shareFileProvider" // 24以上版本文件uri 权限包名加后缀
    const val FirmWarreAutoDownload = "FireWarreAutoDownload" // WiFi下自动下载固件开关
    const val ProductPlanning = "ProductPlanning_%s" // 用户体验改进计划开关：true表示已开
    const val ProductPlanningFlag = "ProductPlanningFlag" // 用户点击同意用户体验改进计划标识位
    const val UserPlanningFlag = "UserPlanningFlag" // 用户点击同意用户体验改进计划标识位
    const val UploadSuccessOtaVersion = "UploadSuccessOtaVersion" // 记录上次ota上次功能版本号
    const val DvtTestUserNo = "DvtTestUserNo" // 产线环境配置的操作员编号 具体需要通产线Mes环境配置一致
    const val DvtTestMacheineNo = "DvtTestMacheineNo" // 产线环境配置的设备类型 例如AR-11等
    const val NetChangeConfig = "NetChangeConfig" // 网络环境标示
    const val AppPrivicyRecord = "AppPrivicyRecord" // app隐私日志上报、用户点击同意隐私政策/用户协议标识位
    const val UserPrivicyAgreement = "UserPrivicyAgreement" // 用户是否同意了隐私政策标识
    const val UserVoiceWakeUpAgreement = "UserVoiceWakeUpAgreement" // 用户是否同意了语音唤醒优化
    const val UserSpeechRecognitionAgreement = "UserSpeechRecognitionAgreement" // 用户是否同意了语音识别优化
    const val UserPermissionAgreement = "UserPermissionAgreement" // 用户权限提示
    const val UserPrivicyUpdate = "UserPrivicyUpdate_%s_%s" // 用户隐私政策更新
    const val UserPrivicyVersion = "UserPrivicyVersion_%s_%s" // 用户同意的用户隐私政策版本
    const val UserPrivicyDescribe = "UserPrivicyDescribe%s_%s" // 用户隐私政策更新文案
    const val UserPrivicyServerVer = "UserPrivicyServerVer_%s_%s" // 用户隐私政策服务器版本
    const val UserAgreementUpdate = "UserAgreementUpdate_%s_%s" // 用户协议更新
    const val UserAgreementVersion = "UserAgreementVersion_%s_%s" // 用户同意的用户协议版本
    const val UserAgreementDescribe = "UserAgreementDescribe_%s_%s" // 用户协议更新文案
    const val UserAgreementServerVer = "UserAgreementServerVer_%s_%s" // 用户协议服务器版本
    const val UserExperienceUpdate = "UserExperienceUpdate_%s_%s" // 用户体验计划更新
    const val UserExperienceVersion = "UserExperienceVersion_%s_%s" // 用户同意的用户体验计划版本
    const val UserExperienceDescribe = "UserExperienceDescribe_%s_%s" // 用户体验计划更新文案
    const val UserExperienceServerVer = "UserExperienceAgreedVer_%s_%s" // 用户体验计划服务器版本
    const val UserLocation = "UserLocation" // 用户选择区域
    const val StagingAccount = "StagingAccount" // 账号环境
    const val PreviewAccount = "PreviewAccount" // 账号环境
    const val DevelopModelOpen = "DevelopModelOpen" // 环境
    const val NeedSelectRegion = "NeedSelectRegion" // 标识首次选择区域
    const val CountryRegionCountry = "CountryRegionCountry" // 国家地区 Country
    const val CountryRegionCode = "CountryRegionCode" // 国家地区 Code
    const val CurrentUrlHost = "CurrentUrlHost" // 当前用户对应的host
    const val language = "zh_CN" // 米家眼镜默认语言汉语
    const val region = "CN" // 米家眼镜默认区域中国
    const val PrivcayUserAgreementDeviceKey: String = "PrivcayUserAgreement_%s_%s_%s_%s"
    const val IsGuidePageConfirm = "IsGuidePageConfirm"
    const val deniedPhoneState = "deniedPhoneState"
    const val FlavorGlobal = "global"
    const val FlavorApp = "app"
    const val ALPHA_PERCENT_5 = 0.5f
    const val ALPHA_PERCENT_0 = 1f
    const val HalfFraction = 0.5F
    const val NormalFraction = 0.285333F
    const val ThreeTenthsFraction = 0.3F
    const val FourTenthsFraction = 0.4F
    const val SixTenthsFraction = 0.55F
    const val SevenTenthsFraction = 0.7F
    const val SwitchFraction = 0.216901F
    const val DefaultFraction = 1F // 组件宽度默认充满屏幕
    const val RoundedFraction = 0.946666F // 圆角样式充满屏幕的百分比
    const val KEY_APP_SHA1 = "appSha1"
    const val KEY_PACKAGE_NAME = "packName"
    const val REMOTE_KEEP_SERVICE = "remoteKeep" // RemoteKeepService进程名
    const val XiaomiPackageName = "com.xiaomi.superhexa" // 小米包名
    const val BaiduMapPackageName = "com.baidu.BaiduMap" // 百度地图包名
    const val BaiduMapMarket = "market://details?id=com.baidu.BaiduMap" // 百度地图
    const val BaiduMapMarketSearch = "market://search?q=com.baidu.BaiduMap" // 搜索百度地图
    const val BaiduMapExtGuideService = "com.baidu.baidumaps.desktopwidget.walkinfo.ExtGuideService" // 百度地图步骑行服务
    const val BaiduMapWalkBroadcast = "com.baidu.baidumaps.extwalkinfo" // 百度步骑行广播
    const val ACTION_WALK_STOP = "android.intent.customize.ACTION_WALK_STOP"
    const val UserNotifySpeechOpen = "UserNotifySpeechOpen_%s" // ss用户是否开启通知播报开关
    const val UserSpeechRateLevel = "UserSpeechRateLevel_%s" // ss用户语音播报播报速率档位
    const val UserSpeechRate = "UserSpeechRate_%s" // ss用户语音播报播报速率
    const val UserNotifySpeechOpenWithModel = "UserNotifySpeechOpenWithModel_%s_%s" // 是否开启通知播报开关
    const val UserSpeechRateLevelWithModel = "UserSpeechRateLevelWithModel_%s_%s" // 语音播报播报速率档位
    const val UserSpeechRateWithModel = "UserSpeechRateWithModel_%s_%s" // 语音播报播报速率
    const val RecordingNoticeDialogWithModel = "RecordingNoticeDialog_%s_%s" // 录音提示弹窗是否需要弹的KEY
    const val ACTION_NOTIFY_SERVICE_STOP = "android.intent.customize.ACTION_NOTIFY_SERVICE_STOP" // 通知播报服务停止广播
    const val DelayTime500 = 500L
    const val WIDGET_KEY = "com.hexa.WIDGET_KEY"
    const val WIDGET_TO_START = "com.hexa.WIDGET_TO_START"
    const val WIDGET_TO_STOP = "com.hexa.WIDGET_TO_STOP"
    const val WIDGET_TO_APP = "com.hexa.WIDGET_TO_APP"
    const val WIDGET_TO_GAME = "com.hexa.WIDGET_TO_GAME"
    const val WIDGET_TO_NOTIFY = "com.hexa.WIDGET_TO_NOTIFY"
    const val WIDGET_TO_AUTO = "com.hexa.WIDGET_TO_AUTO"
    const val WIDGET_TO_FIND = "com.hexa.WIDGET_TO_FIND"
    const val WIDGET_TOAST_NO_SUP = "com.hexa.WIDGET_TOAST_NO_SUP" // 固件不支持
    const val WIDGET_TOAST_NO_SUP_DEVICE = "com.hexa.WIDGET_TOAST_NO_SUP_DEVICE" // 设备不支持
    const val WIDGET_TOAST_NO_TTS = "com.hexa.WIDGET_TOAST_NO_TTS"
    const val RUI_DONG_SDK_SWITCH = "rui_dong_sdk_switch" // 锐动SDK开关KEY
    const val DAILY_POINT_TIME = "daily_point_time_%s" // 埋点上报时间记录
    const val NEED_AUTO_LOGIN = "need_auto_login" // 是否执行自动登录操作
    const val TIAN_QIN_CERT_SKIP = "tian_qin_cert_skip" // 是否跳过天琴证书获取
    const val FRAGMENT_FIRM_TAG = "MiWearDeviceFiremenUpdateDialog" // 固件更新tag
    const val WIFI_CONFIG_RESULT = "wifi_config_result" // 固件配网结果
    const val WIFI_SCAN_RESULT = "wifi_scan_result" // 固件WIFI扫描结果
    const val CAMERA_IS_JOINT = "camera_is_joint" // 设备是否相机协同
    const val CAMERA_IS_RECORDING = "camera_is_recording" // 设备是否正在录像
    const val NEW_BIND_PHONE_SUCCESS = "new_bind_phone_success" // 换绑新设备成功
    const val MIWEAR_DEVICE_IS_DISCONNECTED = "miwear_device_is_disconnected" // 设备是否断开
    const val MIWEAR_DEVICE_IS_UNWEAR = "miwear_device_is_unwear" // 设备未佩戴
    const val ENABLE_DUMP = "enable_dump"
    const val ENABLE_WATERMARK = "enable_watermark"
    const val FIRMWARE_VERSION = "firmwareVersion" // mmkv 缓存固件版本
    const val BACK_LOCATION_REFUSE = "back_location_refuse" // mmkv 缓存固件版本
    const val CAMERA_CHECK_UPGRADE = "camera_check_upgrade"
    const val ACTION_NOTIFY_BROADCAST_FEED_BACK = "com.superhexa.supervision.ACTION_TASK_TRIGGER"
    const val ACTION_NOTIFY_BROADCAST_SCENE_REPRODUCTION = "com.superhexa.supervision.ACTION_TASK_SCENE_TRIGGER"
    const val ACTION_NOTIFY_BROADCAST_DOWNLOAD_PHOTO = "com.superhexa.supervision.ACTION_TASK_DOWNLOAD_TRIGGER"
    const val KEY_TIME_STAMP = "KEY_TIME_STAMP"
    const val KEY_REQUEST_ID = "KEY_REQUEST_ID"
    const val KEY_FEED_BACK_ID = "KEY_FEED_BACK_ID"
    const val LAST_RID = "last_rid" // 上一次小爱语音请求的requestId
    const val KEY_TIME_STAMP_SCENE_LOG = "KEY_TIME_STAMP_SCENE_LOG"
    const val KEY_REQUEST_ID_SCENE_LOG = "KEY_REQUEST_ID_SCENE_LOG"
    const val KEY_REQUEST_TIME_SCENE_LOG = "KEY_REQUEST_TIME_SCENE_LOG"
    const val MEDIA_PLAYER_INFO = "MEDIA_PLAYER_INFO"
    const val SPEAKER_NAME_HISTORY_KEY = "speaker_name_history"
    const val RECORD_STATUS_KEY = "record_status"
    const val TRANSFER_TYPE_ENOUGH_MEMORY = 0
    const val TRANSFER_TYPE_OUT_OF_MEMORY = 1
    const val LAST_DEVICE_ID = "last_device_id"
}
