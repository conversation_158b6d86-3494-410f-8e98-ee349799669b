@file:Suppress("ReturnCount", "MagicN<PERSON>ber", "TooManyFunctions")

package com.superhexa.music.api

import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.IBinder
import android.os.Parcelable
import android.os.RemoteException
import com.superhexa.music.R
import com.superhexa.music.data.ErrorCode
import com.superhexa.music.data.ErrorCode.Companion.NOT_AUTH
import com.superhexa.music.data.ErrorCode.Companion.NOT_INSTALL
import com.superhexa.music.data.PlayState
import com.superhexa.music.data.RadioStationCp
import com.superhexa.music.data.SongData
import com.superhexa.music.data.XMLYConst
import com.superhexa.music.helper.MusicAuthHelper
import com.superhexa.music.helper.MusicAuthHelper.FM
import com.superhexa.music.helper.MusicAuthHelper.setRadioStationSource
import com.superhexa.music.utils.PmUtils
import com.ximalaya.ting.android.host.service.xmcontrolapi.ErrorCodes
import com.ximalaya.ting.android.host.service.xmcontrolapi.IControlConnectionCallBack
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmApiCallback
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmControlApi
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmEventChangCallBack
import com.ximalaya.ting.android.host.service.xmcontrolapi.IXmPlayStatusChangeCallBack
import com.ximalaya.ting.android.host.service.xmcontrolapi.Song
import com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlConstants
import com.ximalaya.ting.android.host.service.xmcontrolapi.XmControlUtil
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.launch
import kotlinx.coroutines.newSingleThreadContext
import timber.log.Timber
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

class XmlyMusicApiImpl(
    val context: Context,
    onPlayInfoChange: () -> Unit,
    onPlayStateChanged: (cp: String, state: Int) -> Unit,
    private val onError: ((cp: String, code: Int?) -> Unit)? = null,
    private var isAuthSuccess: Boolean = false
) : IMusicApi, ServiceConnection, BroadcastReceiver() {
    private var musicApi: IXmControlApi? = null
    private var preExecuteRunnable: Runnable? = null

    //    连接器初始化状态
    private var isApiInit: Boolean = false
    override fun init(context: Context) {
        Timber.tag(TAG).d("喜马拉雅初始化连接...........")
    }

    private var playState: Int = PlayState.UN_KNOW

    private fun playStateChange(newState: Int) {
        this.playState = newState
    }

    private val coroutineExceptionHandler = CoroutineExceptionHandler { _, exception ->
        Timber.tag(TAG).d("$TAG Caught: $exception")
    }

    @OptIn(DelicateCoroutinesApi::class)
    private val workScope = CoroutineScope(newSingleThreadContext(TAG) + coroutineExceptionHandler)

    val xmEventChangeCallback: IXmEventChangCallBack.Stub = object : IXmEventChangCallBack.Stub() {
        @Throws(RemoteException::class)
        override fun onEvnet(paramString: String, result: Bundle) {
            if (XmControlConstants.XM_API_EVENT_PLAY_ERROR == paramString) {
                result.classLoader = Song::class.java.classLoader
                val string = result.getString(XmControlConstants.PLAY_ERROR_TYPE)
                if (XmControlConstants.PLAY_ERROR_TYPE_MOBILE_NET_CANNOT_PLAY == string) {
                    Timber.tag(TAG).d("Can not play under 4G mode")
                    onError?.invoke(RadioStationCp.XMLY, ErrorCode.PLAY_FAIL)
                } else if (XmControlConstants.PLAY_ERROR_TYPE_NO_VIP == string) {
                    Timber.tag(TAG).d("No Vip Privilege")
                    onError?.invoke(RadioStationCp.XMLY, ErrorCode.NO_VIP)
                }
            } else if (XmControlConstants.XM_API_EVENT_PLAY_MODE_CHANGED == paramString) {
                Timber.tag(TAG).d("Play List Changed")
                onPlayInfoChange.invoke()
            } else if (XmControlConstants.XM_API_EVENT_LIKE_STATE_CHANGED == paramString) {
                result.classLoader = Song::class.java.classLoader
                Timber.tag(TAG).d(
                    " Liked Status changed voicec id=%s  isLiked =%s",
                    result.getLong(
                        XmControlConstants.DATA_TYPE_TRACK_ID
                    ),
                    result.getBoolean(XmControlConstants.DATA_TYPE_TRACK_IS_LIKED)
                )
            }
        }
    }
    val xmPlayStatusChangeCallBack: IXmPlayStatusChangeCallBack =
        object : IXmPlayStatusChangeCallBack.Stub() {
            override fun onPlayStart() {
                Timber.tag(TAG).d("Status Change：Playing")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onPlayPause() {
                Timber.tag(TAG).d("Status Change：Pause")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PAUSE)
                playStateChange(PlayState.PAUSE)
            }

            override fun onPlayStop() {
                Timber.tag(TAG).d("Status Change：Stop")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.STOP)
                playStateChange(PlayState.STOP)
            }

            override fun onSoundPlayComplete() {
                Timber.tag(TAG).d("Status Change：Play End ")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PAUSE)
                playStateChange(PlayState.PAUSE)
            }

            override fun onSoundPrepared() {
                Timber.tag(TAG).d("Status Change：Prepared")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onSoundSwitch(p0: Song?, p1: Song?) {
                Timber.tag(TAG).d("Status Change：Music Changed :$p0->$p1")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
                onPlayInfoChange()
            }

            override fun onBufferingStart() {
                Timber.tag(TAG).d("Status Change：Buffering start")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onBufferingStop() {
                Timber.tag(TAG).d("Status Change：Buffer Stop")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onBufferProgress(p0: Int) {
                Timber.tag(TAG).d("Status Change：onBufferProgress")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onPlayProgress(p0: Int, p1: Int) {
                Timber.tag(TAG).d("Status Change：onPlayProgress")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.PLAYING)
                playStateChange(PlayState.PLAYING)
            }

            override fun onError() {
                Timber.tag(TAG).d("Status Change：Error")
                onPlayStateChanged(RadioStationCp.XMLY, PlayState.UN_KNOW)
                playStateChange(PlayState.UN_KNOW)
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.PLAY_FAIL)
            }
        }

    init {
        val xmlyConnectionCallback = object : IControlConnectionCallBack {
            override fun onControlConnected(controlApi: IXmControlApi?, initResult: Bundle?) {
                try {
                    if (controlApi != null) {
                        musicApi = controlApi
                        Timber.tag(TAG).d("initResult：%s", controlApi.toString())
                    }
                    if (initResult == null) {
                        Timber.tag(TAG).e("Can not init API instance")
                    }

                    if (initResult?.getInt(XmControlConstants.RESULT_CODE) == ErrorCodes.ERROR_NO_POLICY_AGREED) {
                        Timber.tag(TAG).e("User did not check the privacy checkBox")
                    }

                    if (!initResult!!.getBoolean(XmControlConstants.DATA_TYPE_VERIFY_RESULT)) {
                        Timber.tag(TAG).e("Signature check failed")
                    }
                    Timber.tag(TAG).e("initResult:%s", initResult.toString())

                    isApiInit = true
                    musicApi?.registerPlayStatueChangeListener(xmPlayStatusChangeCallBack)
                    musicApi?.registerEventListener(
                        object : java.util.ArrayList<String?>() {
                            init {
                                add(XmControlConstants.XM_API_EVENT_PLAY_LIST_CHANGED)
                                add(XmControlConstants.XM_API_EVENT_PLAY_MODE_CHANGED)
                                add(XmControlConstants.XM_API_EVENT_PLAY_ERROR)
                                add(XmControlConstants.XM_API_EVENT_LIKE_STATE_CHANGED)
                            }
                        },
                        xmEventChangeCallback
                    )
                } catch (e: RemoteException) {
                    Timber.e("连接器初始化失败" + e.printStackTrace())
                    isApiInit = false
                }
            }

            override fun onControlDisConnected() {
                musicApi = null
                isApiInit = false
            }
        }
        XmControlUtil.getInstance().bindControl(context, xmlyConnectionCallback)
    }

    override fun bindMusicService(context: Context) {
        if (PmUtils.isAppInstalled(context, XMLYConst.APP_PK_NAME)) {
            XmControlUtil.getInstance().bindControl(context, xmlyConnectionCallback)
        } else {
            onError?.invoke(RadioStationCp.XMLY, NOT_INSTALL)
            Timber.tag(TAG).w("喜马拉雅绑定失败,请检查是否安装.")
        }
    }

    private val xmlyConnectionCallback = object : IControlConnectionCallBack {
        override fun onControlConnected(controlApi: IXmControlApi?, initResult: Bundle?) {
            try {
                if (controlApi != null) {
                    musicApi = controlApi
                    Timber.tag(TAG).d("initResult：%s", controlApi.toString())
                }
                if (initResult == null) {
                    Timber.tag(TAG).e("Can not init API instance")
                }

                if (initResult?.getInt(XmControlConstants.RESULT_CODE) == ErrorCodes.ERROR_NO_POLICY_AGREED) {
                    Timber.tag(TAG).e("User did not check the privacy checkBox")
                }

                if (!initResult!!.getBoolean(XmControlConstants.DATA_TYPE_VERIFY_RESULT)) {
                    Timber.tag(TAG).e("Signature check failed")
                }
                Timber.tag(TAG).e("initResult:%s", initResult.toString())
                isApiInit = true
                musicApi?.registerPlayStatueChangeListener(xmPlayStatusChangeCallBack)
                musicApi?.registerEventListener(
                    object : java.util.ArrayList<String?>() {
                        init {
                            add(XmControlConstants.XM_API_EVENT_PLAY_LIST_CHANGED)
                            add(XmControlConstants.XM_API_EVENT_PLAY_MODE_CHANGED)
                            add(XmControlConstants.XM_API_EVENT_PLAY_ERROR)
                            add(XmControlConstants.XM_API_EVENT_LIKE_STATE_CHANGED)
                        }
                    },
                    xmEventChangeCallback
                )
                MusicAuthHelper.setRadioStationCpAuth(RadioStationCp.XMLY, true)
            } catch (e: RemoteException) {
                Timber.e("连接器初始化失败" + e.printStackTrace())
                isApiInit = false
            }
        }

        override fun onControlDisConnected() {
            musicApi = null
            isApiInit = false
        }
    }

    override fun isAuthSuccess(): Boolean {
        return isAuthSuccess
    }

    private fun launchXimalaya(context: Context, packageName: String = XMLYConst.APP_PK_NAME) {
        val intent = context.packageManager.getLaunchIntentForPackage(packageName)
        if (intent != null) {
            context.startActivity(intent)
        } else {
            onError?.invoke(RadioStationCp.XMLY, NOT_INSTALL)
        }
    }

    override fun doAuthQuest(context: Context, callback: (Boolean) -> Unit) {
        launchXimalaya(context)
        val xmlyCallback = object : IControlConnectionCallBack {
            override fun onControlConnected(controlApi: IXmControlApi?, initResult: Bundle?) {
                if (controlApi != null && initResult?.getBoolean(XmControlConstants.DATA_TYPE_VERIFY_RESULT) == true) {
                    musicApi = controlApi
                    onAuthSuccess()
                    callback(true)
                } else {
                    musicApi = null
                    callback(false)
                }
            }

            override fun onControlDisConnected() {
                musicApi = null
                isApiInit = false
            }
        }

        if (PmUtils.isAppInstalled(context, XMLYConst.APP_PK_NAME)) {
            XmControlUtil.getInstance().bindControl(context, xmlyCallback)
            Timber.tag(TAG).d("musicApi:%s", musicApi != null)
        } else {
            onError?.invoke(RadioStationCp.XMLY, ErrorCode.NOT_AUTH)
            callback(false)
        }
    }

    private fun onAuthSuccess() {
        isAuthSuccess = true
        MusicAuthHelper.setRadioStationCpAuth(RadioStationCp.XMLY, true)
        setRadioStationSource(FM)
        Timber.tag(TAG).d("onBindSuccess")
    }

    override fun isPlaying(): Boolean {
        val bundle: Bundle =
            musicApi?.execute(XmControlConstants.ACTION_IS_PLAYING, null) ?: return false
        Timber.tag(TAG)
            .d("isPlayingResult:%s", bundle.getBoolean(XmControlConstants.DATA_TYPE_IS_PLAYING))
        return bundle.getBoolean(XmControlConstants.DATA_TYPE_IS_PLAYING)
    }

    override fun isPause(): Boolean {
        return !isPlaying()
    }

    override fun play(callback: ((Boolean) -> Unit)?) {
        Timber.tag(TAG).d("MusicAPI:  %s", musicApi.toString())
        execute(XmControlConstants.ACTION_PLAY, null) { res ->
            callback?.invoke(isExecuteOK(res))
        }
    }

    override fun pause(callback: ((Boolean) -> Unit)?) {
        execute(XmControlConstants.ACTION_PAUSE) { res ->
            callback?.invoke(isExecuteOK(res))
        }
    }

    override fun resume(callback: ((Boolean) -> Unit)?) {
        execute(XmControlConstants.ACTION_SEEK_TO, null) { res ->
            callback?.invoke(isExecuteOK(res))
        }
    }

    override fun stop(callback: ((Boolean) -> Unit)?) {
        val res = musicApi?.execute(XmControlConstants.ACTION_STOP, null)
        callback?.invoke(isExecuteOK(res))
    }

    // 播放上一首
    override fun playPre(callback: ((Boolean) -> Unit)?) {
        if (musicApi?.execute(XmControlConstants.ACTION_HAS_PRE, null)
            ?.getBoolean(XmControlConstants.DATA_TYPE_HAS_PRE) == true
        ) {
            val res = musicApi?.execute(XmControlConstants.ACTION_PLAY_PRE, null)
            callback?.invoke(isExecuteOK(res))
        } else {
            Timber.tag(TAG).e("there is no PreviousSong")
        }
    }

    override fun playNext(callback: ((Boolean) -> Unit)?) {
        if (musicApi?.execute(XmControlConstants.ACTION_HAS_NEXT, null)
            ?.getBoolean(XmControlConstants.DATA_TYPE_HAS_PRE) == true
        ) {
            val res = musicApi?.execute(XmControlConstants.ACTION_PLAY_NEXT, null)
            callback?.invoke(isExecuteOK(res))
        } else {
            Timber.tag(TAG).e("there is no NextSong")
        }
    }

    override fun playListAtIndex(
        ids: ArrayList<String>,
        index: Int,
        callback: (Boolean, Int, String) -> Unit
    ) {
        Timber.tag(TAG).d("PlayListAtIndex  Called Params ids:%s,index:%s", ids, index)
        val params = Bundle()
        var musicIds = ""
        ids.forEach {
            musicIds += it
            musicIds += ","
        }
        Timber.tag(TAG).d("Now ids: %s", musicIds)
        params.putString(XmControlConstants.DATA_TYPE_SOUND_LIST, musicIds)
        params.putInt(XmControlConstants.DATA_TYPE_TO_PLAY_INDEX, index)
        executeAsync(
            action = XmControlConstants.ACTION_PLAY_SONG_LIST,
            params = params
        ) { res ->
            callback.invoke(isExecuteOK(res), executeCode(res), executeMessage(res))
        }
    }

    override fun playFavouriteList(
        index: Int,
        type: String,
        callback: (Boolean, Int, String) -> Unit
    ) {
        Timber.tag(TAG).d("playFavouriteList:$index")
        val params = Bundle()
        params.putInt(XmControlConstants.DATA_TYPE_START_POSITION, 0)
        execute(XmControlConstants.ACTION_PLAY_LIKE_SOUND, params) { res ->
            callback.invoke(isExecuteOK(res), executeCode(res), executeMessage(res))
        }
    }

    override fun seekForward(offsetMs: Long, callback: ((Boolean, Int, String) -> Unit)?) {
        Timber.tag(TAG).d("seekForward was called, Params:%s", offsetMs)
        val currentPlayTime = curTime()
        val duration = totalTime()
        val params = Bundle()
        val checkedOffset: Long = when {
            currentPlayTime + offsetMs >= duration -> duration
            else -> currentPlayTime + offsetMs
        }
        params.putInt(XmControlConstants.DATA_TYPE_POSITION, checkedOffset.toInt())
        Timber.tag(TAG).d("seekForward is executing, passed Long params:%s", checkedOffset)
        execute(XmControlConstants.ACTION_SEEK_TO, params) {
            callback?.invoke(true, 1, "操作完成")
        }
    }

    override fun seekBack(offsetMs: Long, callback: ((Boolean, Int, String) -> Unit)?) {
        Timber.tag(TAG).d("seekBack was called, Params:%s", offsetMs)
        val currentPlayTimeResult =
            musicApi?.execute(XmControlConstants.ACTION_GET_CURRENT_TIME, null)
        val currentPlayTime =
            currentPlayTimeResult?.getLong(XmControlConstants.DATA_TYPE_CURRENT_TIME) ?: return
        val checkedOffset: Long = when {
            currentPlayTime - offsetMs <= 0 -> 0
            else -> currentPlayTime - offsetMs
        }
        val params = Bundle()
        params.putInt(XmControlConstants.DATA_TYPE_POSITION, checkedOffset.toInt())
        Timber.tag(TAG).d("seekBack is executing, passed params:%s", checkedOffset)
        execute(
            XmControlConstants.ACTION_SEEK_TO,
            params
        )
        callback?.invoke(true, 1, "操作完成")
    }

    override fun playState(): Int {
        return playState
    }

    override fun curSong(isFore: Boolean, callback: (SongData?) -> Unit) {
        Timber.tag(TAG).d("curSong calling")
        callback.invoke(
            parseSongData(
                musicApi?.execute(
                    XmControlConstants.ACTION_GET_CURRENT_SONG,
                    null
                )
            )
        )
    }

    private fun parseSongData(res: Bundle?): SongData? {
        res?.classLoader = Song::class.java.classLoader
        val song = res?.getParcelable<Song>(XmControlConstants.DATA_TYPE_GET_CURRENT_SONG)
        if (song != null) {
            val songdata = SongData(
                mid = song.id ?: "",
                title = song.title ?: "",
                albumName = song.album.title ?: "",
                singer = song.singer.title ?: ""
            )
            Timber.tag(TAG).d("song data:%s", songdata)
            return songdata
        } else {
            return null
        }
    }

    override fun addToFavourite(mid: String, callback: (Boolean, Int, String) -> Unit) {
        val params = Bundle()
        params.putLong(XmControlConstants.DATA_TYPE_TRACK_ID, mid.toLong())
        Timber.tag(TAG).d("addToFavourite is executing, passed params:%s", mid.toLong())
        executeAsync(
            XmControlConstants.ACTION_ADD_TO_FAVOURITE,
            params
        ) { res ->
            callback.invoke(
                isExecuteOK(res),
                getExecuteCode(res),
                executeMessage(res)
            )
        }
    }

    override fun removeFromFavourite(mid: String, callback: (Boolean, Int, String) -> Unit) {
        val params = Bundle()
        params.putLong(XmControlConstants.DATA_TYPE_TRACK_ID, mid.toLong())
        Timber.tag(TAG).d("removeFromFavourite is executing, passed params:%s", mid.toLong())
        executeAsync(
            XmControlConstants.ACTION_REMOVE_FAVOURITE,
            params
        ) { res ->
            callback.invoke(
                isExecuteOK(res),
                getExecuteCode(ret = res),
                executeMessage(ret = res)
            )
        }
    }

    override fun curTime(): Long {
        Timber.tag(TAG).d("curTime calling")
        return execute(
            XmControlConstants.ACTION_GET_CURRENT_TIME,
            null
        )?.getLong(XmControlConstants.DATA_TYPE_CURRENT_TIME) ?: 0L
    }

    override fun totalTime(): Long {
        Timber.tag(TAG).d("TotalTime calling....")
        val totalTime = execute(
            XmControlConstants.ACTION_GET_TOTAL_TIME,
            null
        )?.getLong(XmControlConstants.DATA_TYPE_TOTAL_TIME) ?: 0L
        Timber.tag(TAG).d("TotalTime called,totalTime:%s", totalTime)
        return totalTime
    }

    private var songList: List<Song> = java.util.ArrayList()
    override fun curPlayList(callback: (ArrayList<String>?) -> Unit) {
        val runnable = Runnable {
            musicApi?.executeAsync(
                XmControlConstants.ACTION_GET_SONG_LIST,
                null,
                object : IXmApiCallback.Stub() {
                    override fun onReturn(res: Bundle) {
                        res.classLoader = Song::class.java.classLoader
                        if (res.getInt(
                                XmControlConstants.RESULT_CODE,
                                ErrorCodes.ERROR_OK
                            ) != ErrorCodes.ERROR_OK
                        ) {
                            Timber.tag(TAG).d("curPlayList error")
                        }
                        val parcelable: ArrayList<Parcelable> =
                            res.getParcelableArrayList(XmControlConstants.DATA_TYPE_SONG_LIST)!!
                        if (parcelable.isEmpty()) {
                            Timber.tag(TAG).d("curPlayList is empty")
                        }
                        songList = parcelable.map { it as Song }
                        Timber.tag(TAG).d(
                            "curPlayList is executing Result: %s,Called XMLY resultCode: %s,IsExecuteOK: %s",
                            executeMessage(ret = res),
                            getExecuteCode(res),
                            isExecuteOK(res)
                        )
                    }
                }
            )
            callback.invoke(songList.map { it.id }.toCollection(ArrayList()))
            Timber.tag(TAG).d("curPlayList called, songList:%s", songList)
        }
        musicApi?.let { runnable.run() } ?: run {
            preExecuteRunnable = runnable
            bindMusicService(context)
        }
    }

    override fun curPlayMode(): String {
        val res = execute(XmControlConstants.ACTION_GET_PLAY_MODE, null) ?: return "null"
        return when (res.getInt(XmControlConstants.DATA_TYPE_PLAY_MODE)) {
            0 -> return PlayMode.LIST
            1 -> return PlayMode.SINGLE
            2 -> return PlayMode.SEQUENCE
            3 -> return PlayMode.LIST
            4 -> return PlayMode.RANDOM
            else -> "Get PlayMode Failed"
        }
    }

    override fun execute(action: String, params: Bundle?): Bundle? {
        val packageInfo = context.packageManager.getPackageInfo(
            context.packageName,
            PackageManager.GET_SIGNATURES
        )
        try{
            for (signature in packageInfo.signatures!!) {
                val cert = signature.toByteArray()
                val md5Fingerprint = getCertificateFingerprint(cert, "MD5")

                Timber.tag(TAG).e("AppSignatureMD5: $md5Fingerprint")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("获取签名失败")
        }
        return kotlin.runCatching {
            val ret = musicApi?.execute(action, params) ?: kotlin.run {
                preExecuteRunnable = Runnable { execute(action, params) }
                bindMusicService(context)
                null
            }
            handleExecuteResult(action, ret)
        }.getOrElse {
            Timber.tag(TAG).e(it, "Music api error.")
            onServiceDisconnected(null)
            null
        }
    }

    private fun getCertificateFingerprint(cert: ByteArray, algorithm: String): String? {
        return try {
            val md = MessageDigest.getInstance(algorithm)
            val publicKey = md.digest(cert)
            val hexString = StringBuilder()
            for (aPublicKey in publicKey) {
                val hex = Integer.toHexString(0xFF and aPublicKey.toInt())
                if (hex.length == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }
            hexString.toString()
        } catch (e: NoSuchAlgorithmException) {
            null
        }
    }

    override fun execute(action: String, params: Bundle?, callback: (Bundle?) -> Unit) {
        Timber.tag(TAG).d("execute with callback:$action,$params")
        if (!checkAuthStatus()) return
        val packageInfo = context.packageManager.getPackageInfo(
            context.packageName,
            PackageManager.GET_SIGNATURES
        )
        try{
            for (signature in packageInfo.signatures!!) {
                val cert = signature.toByteArray()
                val md5Fingerprint = getCertificateFingerprint(cert, "MD5")

                Timber.tag(TAG).e("AppSignatureMD5: $md5Fingerprint")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("获取签名失败")
        }
        workScope.launch {
            runCatching {
                val res = musicApi?.execute(action, params) ?: kotlin.run {
                    preExecuteRunnable = Runnable { execute(action, params, callback) }
                    bindMusicService(context)
                    null
                }
                callback.invoke(handleExecuteResult(action, res))
            }.getOrElse {
                Timber.tag(TAG).e(it, "Music api error.")
            }
        }
    }

    override fun executeAsync(
        action: String,
        params: Bundle?,
        callback: (Bundle?) -> Unit
    ) {
        Timber.tag(TAG).d("executeAsync:$action,$params")
        if (!checkAuthStatus()) return
        val packageInfo = context.packageManager.getPackageInfo(
            context.packageName,
            PackageManager.GET_SIGNATURES
        )
        try{
            for (signature in packageInfo.signatures!!) {
                val cert = signature.toByteArray()
                val md5Fingerprint = getCertificateFingerprint(cert, "MD5")

                Timber.tag(TAG).e("AppSignatureMD5: $md5Fingerprint")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("获取签名失败")
        }
        workScope.launch {
            runCatching {
                musicApi?.let {
                    musicApi?.executeAsync(
                        action,
                        params,
                        object : IXmApiCallback.Stub() {
                            override fun onReturn(res: Bundle) {
                                Timber.tag(TAG).d(
                                    "executeAsync result: %s,Called XMLY resultCode: %s,IsExecuteOK: %s",
                                    executeMessage(ret = res),
                                    getExecuteCode(res),
                                    isExecuteOK(res)
                                )
                                callback.invoke(handleExecuteResult(action, res))
                            }
                        }
                    )
                } ?:run {
                    bindMusicService(context)
                    preExecuteRunnable = Runnable { executeAsync(action, params, callback) }
                }
            }.getOrElse {
                Timber.tag(TAG).e(it, "Music api error.")
                onServiceDisconnected(null)
            }
        }
    }

    private fun handleExecuteResult(action: String, res: Bundle?): Bundle? {
        if (action == XmControlConstants.ACTION_SEEK_TO) {
            res?.putInt(XmControlConstants.RESULT_CODE, ErrorCodes.ERROR_OK)
        }
        val errorCode = getExecuteCode(res)
        Timber.tag(TAG).d("handleExecuteResult:$action,$errorCode")
        when (errorCode) {
            ErrorCodes.ERROR_OK -> {}
            ErrorCodes.ERROR_VERIFY_ERROR -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.NOT_AUTH)
            }

            ErrorCodes.ERROR_NEED_USER_AUTHENTICATION -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.NOT_LOGIN)
            }

            ErrorCodes.ERROR_GET_DATA_NETWORK,
            ErrorCodes.ERROR_FAILED_TO_QUERY_FROM_SONG_ID -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.REQUEST_FAIL)
            }

            ErrorCodes.ERROR_NO_POLICY_AGREED -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.NOT_PRIVACY)
            }

            ErrorCodes.ERROR_PLAY_UNKNOWN,
            ErrorCodes.ERROR_API_UNSUPPORTED_ACTION -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.OTHER)
            }

            ErrorCodes.ERROR_NO_HAVE_LIKE_SONG -> {
                onError?.invoke(RadioStationCp.XMLY, ErrorCode.NO_FAVORITES)
            }
        }
        return res
    }

    override fun setPlayMode(playMode: String, callback: ((Boolean, Int, String) -> Unit)?) {
        val params = Bundle()
        when (playMode) {
            PlayMode.LIST -> params.putInt(XmControlConstants.DATA_TYPE_PLAY_MODE, 2)
            PlayMode.SINGLE -> params.putInt(XmControlConstants.DATA_TYPE_PLAY_MODE, 1)
            PlayMode.SEQUENCE -> params.putInt(XmControlConstants.DATA_TYPE_PLAY_MODE, 3)
            PlayMode.RANDOM -> params.putInt(XmControlConstants.DATA_TYPE_PLAY_MODE, 4)
        }
        Timber.tag(TAG).d("setPlayMode is calling, passed params:%s", params)
        val res = musicApi?.execute(XmControlConstants.ACTION_SET_PLAY_MODE, params)
        Timber.tag(TAG).d(
            "setPlayMode is executing Result: %s,Called XMLY resultCode: %s,IsExecuteOK: %s",
            executeMessage(ret = res),
            getExecuteCode(res),
            isExecuteOK(res)
        )
        callback?.invoke(isExecuteOK(res), executeCode(res), executeMessage(res))
    }
    private fun checkAuthStatus():Boolean {
        if (!PmUtils.isAppInstalled(context, XMLYConst.APP_PK_NAME)) {
            onError?.invoke(RadioStationCp.XMLY,NOT_INSTALL)
            MusicAuthHelper.setRadioStationCpAuth(RadioStationCp.XMLY,false)
            return false
        }
        if (!MusicAuthHelper.isRadioStationCpAuthed(RadioStationCp.XMLY)) {
            onError?.invoke(RadioStationCp.XMLY, NOT_AUTH)
            return false
        }
        return true
    }

    override fun onError(cp: String, code: Int?) {
        Timber.tag(TAG).d("$cp,$code")
        onError?.invoke(cp, code)
    }

    override fun release() {
        Timber.tag(TAG).d("release")
        musicApi = null
        isAuthSuccess = false
    }

    override fun onServiceConnected(p0: ComponentName?, p1: IBinder?) {
        Timber.tag(TAG).d("[XMLY] SERVICE CONNECTED")
        isAuthSuccess = true
    }

    override fun onServiceDisconnected(p0: ComponentName?) {
        Timber.tag(TAG).d("[XMLY] SERVICE DISCONNECTED")
        musicApi = null
    }

    override fun onReceive(p0: Context?, p1: Intent?) {
        Timber.tag(TAG).d("onReceive:$p1")
    }

    private fun isExecuteOK(ret: Bundle?): Boolean {
        return ret?.getInt(XmControlConstants.RESULT_CODE) == ErrorCodes.ERROR_OK
    }

    private fun getExecuteCode(ret: Bundle?): Int {
        return ret?.getInt(XmControlConstants.RESULT_CODE) ?: -1
    }

    private fun executeCode(ret: Bundle?): Int {
        return ret?.getInt(XmControlConstants.RESULT_CODE, -1) ?: 0
    }

    private fun executeMessage(ret: Bundle?): String {
        when (ret?.getInt(XmControlConstants.RESULT_CODE)) {
            ErrorCodes.ERROR_OK -> return context.getString(R.string.libs_xmly_error_message_ok)
            ErrorCodes.ERROR_API_UNSUPPORTED_ACTION ->
                return context.getString(R.string.libs_xmly_error_message_un_support_action)
            ErrorCodes.ERROR_NEED_USER_AUTHENTICATION ->
                return context.getString(R.string.libs_xmly_error_message_need_login)
            ErrorCodes.ERROR_FAILED_TO_QUERY_FROM_SONG_ID ->
                return context.getString(R.string.libs_xmly_error_message_failed_query_song)
            ErrorCodes.ERROR_GET_DATA_NETWORK ->
                return context.getString(R.string.libs_xmly_error_message_network_err)
            ErrorCodes.ERROR_VERIFY_ERROR ->
                return context.getString(R.string.libs_xmly_error_message_verify_err)
            ErrorCodes.ERROR_NO_HAVE_LIKE_SONG ->
                return context.getString(R.string.libs_xmly_error_message_no_like_list)
            ErrorCodes.ERROR_NO_POLICY_AGREED ->
                return context.getString(R.string.libs_xmly_error_message_no_policy)
            ErrorCodes.ERROR_NO_HISTORY ->
                return context.getString(R.string.libs_xmly_error_message_no_history)
            ErrorCodes.ERROR_PLAY_UNKNOWN ->
                return context.getString(R.string.libs_xmly_error_message_unknown_err)
        }
        return ret?.getInt(XmControlConstants.RESULT_CODE).toString()
    }

    @Retention(AnnotationRetention.SOURCE)
    annotation class PlayMode {
        companion object {
            // 顺序播放.
            const val SEQUENCE = "SEQUENCE"

            // 列表循环.
            const val LIST = "LIST"

            // 单曲循环.
            const val SINGLE = "SINGLE"

            // 随机播放.
            const val RANDOM = "RANDOM"
        }
    }

    companion object {
        const val TAG = "XMLY"
    }
}
