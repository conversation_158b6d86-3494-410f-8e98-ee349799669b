package com.xiaomi.aivs.player

import android.content.Context
import android.content.Intent
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import android.view.KeyEvent
import androidx.annotation.Keep
import com.xiaomi.ai.api.PlaybackController
import com.xiaomi.ai.api.common.InstructionPayload
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.engine.proxy.SpeechEngineProxyImpl
import timber.log.Timber

class MPLayer(
    val context: Context,
    private val utteranceListener: UtteranceListener
) {
    // ExoPlayer 实例（需外部注入或内部创建）
    private var exoPlayer: SimpleExoPlayer? = null
    private val playDeque = ArrayDeque<String>()
    private var dialogId: String? = null
    private var currentPlayingIndex = 0
    private var currentAudioType: SpeechEngineProxyImpl.Companion.AudioType? = null
    private var lastControlTime = INVALID_CONTROL_TIME
    private val handler = Handler(Looper.getMainLooper())
    private var timeoutRunnable: Runnable? = null
    private var mediaPlayer: MediaPlayer? = null
    private var isUserPause: Boolean = false

    // 添加 MediaSessionCompat 和回调
    private var mediaSession: MediaSessionCompat? = null

    @Suppress("ReturnCount")
    private val mediaSessionCallback by lazy {
        object : MediaSessionCompat.Callback() {
            override fun onMediaButtonEvent(mediaButtonEvent: Intent?): Boolean {
                val keyEvent =
                    mediaButtonEvent?.getParcelableExtra(Intent.EXTRA_KEY_EVENT) as? KeyEvent
                if (keyEvent?.action == KeyEvent.ACTION_DOWN) {
                    when (keyEvent.keyCode) {
                        KeyEvent.KEYCODE_MEDIA_PLAY -> {
                            resume()
                            onPlay()
                            Timber.tag(TAG).d("MediaSession onMediaButtonEvent, keyEvent -> KEYCODE_MEDIA_PLAY")
                            return true
                        }

                        KeyEvent.KEYCODE_MEDIA_PAUSE -> {
                            if (isPlaying()) {
                                userPauseChane("KEYCODE_MEDIA_PAUSE", true)
                            }
                            pause()
                            onPause()
                            Timber.tag(TAG).d("MediaSession onMediaButtonEvent, keyEvent -> KEYCODE_MEDIA_PAUSE")
                            return true
                        }

                        KeyEvent.KEYCODE_MEDIA_STOP -> {
                            stopAll("KEYCODE_MEDIA_STOP")
                            onStop()
                            Timber.tag(TAG).d("MediaSession onMediaButtonEvent, keyEvent -> KEYCODE_MEDIA_STOP")
                            return true
                        }

                        KeyEvent.KEYCODE_MEDIA_NEXT -> {
                            Timber.tag(TAG).d("MediaSession onMediaButtonEvent, keyEvent -> KEYCODE_MEDIA_NEXT")
                            forcePlayNext()
                            onSkipToNext()
                            return true
                        }

                        KeyEvent.KEYCODE_MEDIA_PREVIOUS -> {
                            Timber.tag(TAG).d("MediaSession onMediaButtonEvent, keyEvent -> KEYCODE_MEDIA_PREVIOUS")
                            forcePlayPrev()
                            onSkipToPrevious()
                            return true
                        }
                    }
                }
                return false
            }
        }
    }

    init {
        exoPlayer = SimpleExoPlayer(AiSpeechEngine.INSTANCE.getDeFaultExoPlayer())
    }

    private fun initializeMediaSession() {
        mediaSession = MediaSessionCompat(context, "MPLayerMediaSession").apply {
            setFlags(
                MediaSessionCompat.FLAG_HANDLES_MEDIA_BUTTONS or
                    MediaSessionCompat.FLAG_HANDLES_TRANSPORT_CONTROLS
            )
            setCallback(mediaSessionCallback)
        }
    }

    // 确保丢失焦点, mediaSession回调失效的情况下仍能操作
    fun handleMediaControl(
        payload: InstructionPayload
    ) {
        val currentMediaType = AiSpeechEngine.INSTANCE.getCurrentMediaType()
        Timber.tag(TAG).d("handleMediaControl:$currentMediaType,$payload")
        if (playDeque.isNotEmpty() &&
            currentMediaType == SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO
        ) {
            when (payload) {
                is PlaybackController.ContinuePlaying -> resume()
                is PlaybackController.Pause -> {
                    if (isPlaying()) {
                        userPauseChane("handleMediaControl", true)
                    }
                    pause()
                }
                is PlaybackController.Stop -> stopAll("handleMediaControl")
                is PlaybackController.Next -> forcePlayNext()
                is PlaybackController.Prev -> forcePlayPrev()
                else -> Unit
            }
        }
    }

    // 播放接口保持兼容
    fun play(mediaSource: MediaSource, onComplete: (() -> Unit)? = null) {
        Timber.tag(TAG).d("play:$mediaSource,${mediaSource.urls.size}")
        stopAll("play")
        playDeque.addAll(mediaSource.urls)
        <EMAIL> = mediaSource.dialogId
        currentPlayingIndex = 0
        when (mediaSource) {
            is TtsSource -> {
                currentAudioType = SpeechEngineProxyImpl.Companion.AudioType.TTS
            }

            is LongAudioSource -> {
                currentAudioType = SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO
                initializeMediaSession()
            }
        }
        AiSpeechEngine.INSTANCE.saveCurrentMediaType(getCurrentAudioType())
        mediaSession?.let {
            if (it.controller != null) {
                Timber.tag(TAG).d("MediaSession isActive -> true")
                it.isActive = true
            }
        }
        exoPlayer?.setPlayerReadyListener(
            object : SimpleExoPlayer.PlayerReadyListener {
                override fun onPlayerReady() {
                    Timber.tag(TAG).i("ExoPlayer is ready, onUtteranceStart")
                    utteranceListener.onUtteranceStart(dialogId, true)
                    exoPlayer?.clearPlayerReadyListener()
                }
            }
        )
        playNext(dialogId, onComplete)
    }

    private fun playNext(dialogId: String?, onComplete: (() -> Unit)? = null) {
        Timber.tag(TAG).d("playNext:${currentPlayingIndex + 1}/${playDeque.size}")
        nextPlayUrl()?.let {
            play(dialogId, it) {
                currentPlayingIndex += 1
                playNext(dialogId, onComplete)
            }
        } ?: run {
            onComplete?.invoke()
            utteranceListener.onUtteranceDone(dialogId, true)
        }
    }

    // ExoPlayer 播放逻辑
    private fun play(dialogId: String?, url: String, onComplete: (() -> Unit)? = null) {
        <EMAIL> = dialogId
        Timber.tag(TAG).d("play:$dialogId,$url")
        userPauseChane("play", false)
        if (AiSpeechEngine.INSTANCE.getCurrentMediaType() != getCurrentAudioType()) {
            AiSpeechEngine.INSTANCE.saveCurrentMediaType(getCurrentAudioType())
        }
        exoPlayer?.play(url, onComplete = onComplete)
        mediaSession?.setPlaybackState(buildPlaybackState(PlaybackStateCompat.STATE_PLAYING))
    }

    fun playTipSound(resourceId: Int, complete: (() -> Unit)? = null) {
        Timber.tag(TAG).d("playTipSound:$resourceId")
        stopAll("playTipSound")
        utteranceListener.onUtteranceStart(dialogId, isUrl = false, isLocalCorpus = true)
        playTipSoundImpl(resourceId) {
            complete?.invoke()
            utteranceListener.onUtteranceDone(dialogId, isUrl = false, isLocalCorpus = true)
        }
    }

    fun userPauseChane(reason: String, value: Boolean) {
        isUserPause = value
        Timber.w("userPauseChane $reason $value")
    }

    fun isUserPause(): Boolean {
        Timber.w("isUserPause $isUserPause")
        return isUserPause
    }

    private fun playTipSoundImpl(resourceId: Int, complete: (() -> Unit)? = null) {
        Timber.d("playTipSoundImpl :$resourceId")
        // GTKGLASS-13200 CompletionListener可能不会回调，加个超时兜底
        timeoutRunnable = Runnable {
            Timber.w("playTipSoundImpl MediaPlayer playback timeout")
            mediaPlayer?.stop()
            complete?.invoke()
            cleanup()
        }
        kotlin.runCatching {
            mediaPlayer = MediaPlayer.create(context, resourceId)?.also { player ->
                player.setOnCompletionListener {
                    removeTimeout()
                    complete?.invoke()
                    Timber.d("playTipSoundImpl Complete.")
                    it.release()
                }
                player.setOnErrorListener { mp, what, extra ->
                    removeTimeout()
                    complete?.invoke()
                    Timber.e("playTipSoundImpl error: what=$what, extra=$extra")
                    mp.release()
                    true
                }
                player.start()
                Timber.d("playTipSoundImpl:${player.duration}")
                timeoutRunnable?.let { handler.postDelayed(it, PLAY_TIPS_TIME_OUT) }
            }
        }.onFailure {
            complete?.invoke()
            cleanup()
            Timber.e(it, "MediaPlayer创建失败")
        }
    }

    private fun removeTimeout() {
        Timber.e("removeTimeout $timeoutRunnable")
        timeoutRunnable?.let {
            handler.removeCallbacks(it)
            timeoutRunnable = null
        }
    }

    private fun cleanup() {
        Timber.e("cleanup $mediaPlayer")
        mediaPlayer?.release()
        mediaPlayer = null
        removeTimeout()
    }

    // 播放控制接口
    fun stopAll(reason: String) {
        Timber.tag(TAG).d("stopAll:$reason")
        playDeque.clear()
        stop()
        mediaSession?.let {
            if (it.controller != null) {
                Timber.tag(TAG).d("MediaSession isActive -> false")
                it.isActive = false
                Timber.tag(TAG).d("release MediaSession")
                it.release()
                mediaSession = null
            }
        }
        currentAudioType = null
        lastControlTime = INVALID_CONTROL_TIME
    }

    private fun stop() {
        exoPlayer?.apply {
            stop()
            clearPlayerReadyListener()
        }
        utteranceListener.onUtteranceStop(dialogId, true)
        dialogId = null
    }

    fun pause() {
        Timber.tag(TAG).d("pause")
        exoPlayer?.playOrPause(PlayerController.PlayState.STATE_PAUSE)
        mediaSession?.setPlaybackState(buildPlaybackState(PlaybackStateCompat.STATE_PAUSED))
    }

    fun resume() {
        Timber.tag(TAG).d("resume")
        userPauseChane("resume", false)
        exoPlayer?.playOrPause(PlayerController.PlayState.STATE_PLAY)
        mediaSession?.setPlaybackState(buildPlaybackState(PlaybackStateCompat.STATE_PLAYING))
    }

    fun isPlaying(): Boolean {
        val isPlaying = exoPlayer?.isPlaying() == true || playDeque.isNotEmpty()
        Timber.tag(TAG).d("isPlaying:$isPlaying")
        return isPlaying
    }

    private fun isAudioPlaying(): Boolean {
        val isPlaying = exoPlayer?.isPlaying() == true
        Timber.tag(TAG).d("isAudioPlaying:$isPlaying")
        return isPlaying
    }

    fun isLongAudioPlaying(): Boolean {
        val isLongAudioPlaying = isAudioPlaying() &&
            SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO == getCurrentAudioType()
        Timber.tag(TAG).d("isLongAudioPlaying:$isLongAudioPlaying")
        return isLongAudioPlaying
    }

    fun isLongAudioPausing(): Boolean {
        val isLongAudioPausing = isPausing() &&
            SpeechEngineProxyImpl.Companion.AudioType.LONG_AUDIO == getCurrentAudioType()
        Timber.tag(TAG).d("isLongAudioPausing:$isLongAudioPausing")
        return isLongAudioPausing
    }

    fun isPausing(): Boolean {
        val isPausing = exoPlayer?.isPausing() == true
        Timber.tag(TAG).d("isPausing:$isPausing")
        return isPausing
    }

    // 播放下一首/上一首
    private fun forcePlayNext() {
        val now = System.currentTimeMillis()
        if (lastControlTime > 0 && now - lastControlTime < MIN_CONTROL_INTERVAL) return
        Timber.tag(TAG).d("forcePlayNext")
        lastControlTime = now
        exoPlayer?.let {
            if (it.isPlaying()) it.stop()
            if (currentPlayingIndex < playDeque.size - 1) {
                currentPlayingIndex += 1
            } else {
                currentPlayingIndex = 0
            }
            playNext(dialogId)
        }
    }

    private fun forcePlayPrev() {
        val now = System.currentTimeMillis()
        if (lastControlTime > 0 && now - lastControlTime < MIN_CONTROL_INTERVAL) return
        Timber.tag(TAG).d("forcePlayPrev")
        lastControlTime = now
        exoPlayer?.let {
            if (it.isPlaying()) it.stop()
            if (currentPlayingIndex > 0) {
                currentPlayingIndex -= 1
            } else {
                currentPlayingIndex = playDeque.size - 1
            }
            playNext(dialogId)
        }
    }

    private fun nextPlayUrl(): String? {
        val nextUrl = playDeque.getOrNull(currentPlayingIndex)
        Timber.tag(TAG).d("nextPlayUrl $nextUrl, ${currentPlayingIndex + 1}, ${playDeque.size}")
        return nextUrl
    }

    fun getCurrentAudioType(): SpeechEngineProxyImpl.Companion.AudioType? {
        Timber.tag(TAG).d("currentAudioType $currentAudioType")
        return currentAudioType
    }

    // 释放资源
    fun release() {
        Timber.tag(TAG).d("release")
        playDeque.clear()
        exoPlayer?.apply {
            stop() // 先停止播放
            release() // 再释放资源
        }
        exoPlayer = null // 置空引用
        mediaSession?.let {
            if (it.controller != null) {
                Timber.tag(TAG).d("MediaSession isActive -> false")
                it.isActive = false
                Timber.tag(TAG).d("release MediaSession")
                it.release()
                mediaSession = null
            }
        }
        currentAudioType = null
    }

    @Suppress("MagicNumber")
    private fun buildPlaybackState(state: Int): PlaybackStateCompat {
        return PlaybackStateCompat.Builder()
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                    PlaybackStateCompat.ACTION_PAUSE or
                    PlaybackStateCompat.ACTION_PLAY_PAUSE or
                    PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                    PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS
            )
            .setState(
                state,
                0, // 当前播放位置（毫秒）
                1.0f // 播放速率（1.0 正常速度）
            )
            .build()
    }

    @Keep
    sealed class MediaSource(
        open val dialogId: String?,
        open val urls: List<String>
    )

    @Keep
    data class TtsSource(
        override val dialogId: String?,
        override val urls: List<String>
    ) : MediaSource(dialogId, urls)

    @Keep
    data class LongAudioSource(
        override val dialogId: String?,
        override val urls: List<String>
    ) : MediaSource(dialogId, urls)

    companion object {
        private const val TAG = "MPLayer"

        private const val INVALID_CONTROL_TIME = -1L
        private const val MIN_CONTROL_INTERVAL = 300L
        private const val PLAY_TIPS_TIME_OUT = 4000L
    }
}
