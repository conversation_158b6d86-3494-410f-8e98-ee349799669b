@file:Suppress("TooGenericExceptionCaught", "LongParameterList")

package com.xiaomi.aivs.engine.helper

import com.google.firebase.crashlytics.buildtools.reloc.org.apache.http.util.ByteArrayBuffer
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.bean.ChatRecord
import com.xiaomi.ai.api.Common.FileConfig
import com.xiaomi.ai.api.MultiModal
import com.xiaomi.ai.api.MultiModal.ImageUnderstand
import com.xiaomi.aivs.AiSpeechEngine
import com.xiaomi.aivs.data.DialogNode
import com.xiaomi.aivs.data.KeyWorld
import com.xiaomi.aivs.data.model.ImageDataCache
import com.xiaomi.aivs.data.model.ImageEventPair
import com.xiaomi.aivs.engine.ISpeechEngine
import com.xiaomi.aivs.engine.event.DeviceEvent
import com.xiaomi.aivs.engine.state.EngineStateMachine
import io.objectbox.BoxStore
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.math.ceil

class ImageEventProcess(val engine: ISpeechEngine) {

    companion object {
        private const val IMAGE_OCR_BUFFER_CAPACITY = 2048
        private const val IMAGE_UNDERSTAND_DELAY = 300L
        private const val IMAGE_CACHE_DELAY = 15_000L
        const val IMAGE_ID_PREFIX = "multimodal_glasses_"
    }

    private var imageChunkCount = 0
    private var imageChunkIndex = 0
    private var imageEventPair = ImageEventPair()
    private var imageDataCache: ImageDataCache? = null

    // 存储图片的<ImageUnderstandId,QueryId>>
    private var imageQueryResMap = mutableMapOf<String, String>()

    private var imageOcrData = ByteArrayBuffer(IMAGE_OCR_BUFFER_CAPACITY)
    private var imageUnderstandJob: Job? = null
    private var imageDataCacheCleanJob: Job? = null

    private val boxStore: BoxStore = DbHelper.getBoxStore()
    private val boxFor by lazy { boxStore.boxFor(ChatRecord::class.java) }

    fun onUploadMultiModalEvent(
        transactionId: String?,
        dialogId: String,
        payload: String?,
        isPush: Boolean,
        postEnd: (() -> Unit)? = null
    ) {
        Timber.d("onUploadMultiModalEvent:$dialogId")
        imageEventPair.resetImageData()
        imageEventPair.dialogId = dialogId
        imageEventPair.isPush = isPush
        queryUserQuery(dialogId) { query -> query?.let { imageEventPair.query = query } }

        if (imageDataCache?.isValid() == true) {
            val isImageComplete = imageDataCache?.isImageComplete()
            if (isImageComplete == true) {
                onImagePostForCacheEnd(ImageFileHandler.ImageType.CROPPED, postEnd)
            } else {
                Timber.d("image is not complete:$imageDataCache")
            }
        } else {
            Timber.d("image is not valid:${imageDataCache?.timestamp},${System.currentTimeMillis()}")
            AiSpeechEngine.INSTANCE.sendEventToDevice(
                transactionId,
                dialogId,
                payload,
                DeviceEvent.IMAGE_CAPTURE_AND_TRANS
            )
            // post image stream started event for image engine in advance: warm up ws connection
            postImageStreamStarted()
        }
    }

    private fun queryUserQuery(dialogId: String?, callback: (String?) -> Unit) {
        AiSpeechEngine.INSTANCE.queryUserQuery(dialogId) { query ->
            Timber.d("queryUserQuery:$query")
            callback.invoke(query)
        }
    }

    fun postImageBegin(
        totalSize: Int,
        chunkSize: Int,
        type: ImageFileHandler.ImageType,
        requestId: String
    ): String {
        Timber.d("postImageBegin:${imageEventPair.dialogId},$type")
        // 决定图片ID
        return if (isPostImageForOnlineReady() &&
            (type == ImageFileHandler.ImageType.CROPPED || type == ImageFileHandler.ImageType.UNKNOWN)
        ) {
            postImageBeginForOnline(totalSize, chunkSize)
        } else {
            postImageBeginForCache(totalSize, chunkSize, requestId)
        }
    }

    private fun isNeedCacheType(type: ImageFileHandler.ImageType): Boolean {
        // 缩略图或原图
        val isNeedCacheType =
            ImageFileHandler.ImageType.ORIGINAL == type || ImageFileHandler.ImageType.THUMBNAIL == type
        Timber.i("isNeedCacheType: $isNeedCacheType")
        return isNeedCacheType
    }

    private fun postImageStreamStarted() {
        val imageStreamStarted = MultiModal.ImageStreamStarted()
        imageStreamStarted.setUploadOnly(true)
        imageStreamStarted.setImageConfig(
            FileConfig().apply {
                setPrefix(IMAGE_ID_PREFIX)
                setContentType("image/jpeg")
            }
        )
        imageStreamStarted.setIsUploadAsync(true)
        val imageStreamStartedId = engine.postImageEvent(imageStreamStarted)
        onImageStreamStarted(imageStreamStartedId)
    }

    private fun onImageStreamStarted(requestId: String) {
        Timber.d("onImageStreamStarted:$requestId")
        imageEventPair.requestId = requestId
    }

    private fun postImageBeginForOnline(totalSize: Int, chunkSize: Int): String {
        AiSpeechEngine.INSTANCE.cancelTimer("开始上传图片.")
        AiSpeechEngine.INSTANCE.onQueryRecognize(
            requestId(),
            dialogId = dialogId(),
            query = "postImageDataForOnline",
            isFinal = true,
            isFromPostImageForLinkImgId = true,
            instructionJson = null
        )
        imageChunkIndex = 0
        imageChunkCount = ceil(totalSize.toDouble() / chunkSize.toDouble()).toInt()
        val uploadRid = imageEventPair.requestId
        Timber.d("postImageBeginForOnline:$uploadRid,$totalSize,$chunkSize")
        return uploadRid
    }

    private fun postImageBeginForCache(totalSize: Int, chunkSize: Int, requestId: String): String {
        val timestamp = System.currentTimeMillis()
        Timber.d("postImageBeginForCache:$requestId,$totalSize,$chunkSize")
        // 初始化Cache
        imageDataCache = ImageDataCache(
            requestId = requestId,
            timestamp = timestamp,
            totalSize = totalSize,
            data = ByteArrayBuffer(totalSize)
        )
        imageDataCacheCleanJob?.cancel()
        imageDataCacheCleanJob = MainScope().launch {
            delay(IMAGE_CACHE_DELAY)
            imageDataCache = null
        }
        return requestId
    }

    fun postImageData(
        requestId: String,
        bytes: ByteArray?,
        format: String,
        size: Pair<Int, Int>,
        type: ImageFileHandler.ImageType = ImageFileHandler.ImageType.CROPPED,
        postEnd: (() -> Unit)? = null
    ) {
        if (isNeedCacheType(type)) {
            postImageDataForCache(requestId, bytes, format, size, type = type, postEnd = postEnd)
        } else {
            imageDataCache?.let {
                postImageDataForCache(
                    requestId,
                    bytes,
                    format,
                    size,
                    type = type,
                    postEnd = postEnd
                )
            } ?: run {
                postImageDataForOnline(
                    requestId,
                    bytes,
                    format,
                    size,
                    type = type,
                    postEnd = postEnd
                )
            }
        }
        if (!isNeedCacheType(type)) {
            ImageFileHandler.onReceiveData(
                AiSpeechEngine.INSTANCE.appContext,
                requestId,
                type,
                bytes
            )
        }
    }

    private fun postImageUnderStandDonePrepare() {
        Timber.d("postImageUnderStandDonePrepare")
        val delayMs = if (imageEventPair.ocr.isNullOrEmpty()) IMAGE_UNDERSTAND_DELAY else 0L
        imageUnderstandJob = MainScope().launch {
            delay(delayMs)
            postImageUnderStandDone()
        }
    }

    private fun postImageDataForOnline(
        requestId: String,
        bytes: ByteArray?,
        format: String,
        size: Pair<Int, Int>,
        fromCache: Boolean = false,
        type: ImageFileHandler.ImageType,
        postEnd: (() -> Unit)? = null
    ) {
        Timber.d("dialogID:${imageEventPair.dialogId}")
        // 只有缩略图传云端
        if (type != ImageFileHandler.ImageType.CROPPED) {
            Timber.i("invalid ImageType $type")
            return
        }
        val chunk = imageChunkIndex to imageChunkCount
        engine.postImageData(requestId, format, size, chunk, bytes)
        Timber.d("postImageDataForOnline:$requestId,$imageChunkIndex,${bytes?.size},$fromCache,$type")
        imageChunkIndex++
        if (imageChunkIndex == imageChunkCount) {
            Timber.d("postImageDataForOnlineEnd:$requestId")
            engine.postImageEnd(imageRequestId = requestId, dialogId = dialogId())
            ImageFileHandler.closeFile(requestId)
            // 上传至网络成功后调用
            postEnd?.invoke()
            if (!fromCache) {
                postImageUnderStandDonePrepare()
            }
            // 蓝牙传图后进入NLP WAIT大模型思考等待
            EngineStateMachine.onDialogNode(DialogNode.NLP_WAIT, true, true)
            ImageFileHandler.closeFile(requestId)
        }
    }

    // 添加到Cache变量
    private fun postImageDataForCache(
        requestId: String,
        bytes: ByteArray?,
        format: String,
        size: Pair<Int, Int>,
        type: ImageFileHandler.ImageType,
        postEnd: (() -> Unit)? = null
    ) {
        Timber.d(
            "postImageDataForCache:$requestId,${imageDataCache?.totalSize}," +
                    "${bytes?.size},${imageDataCache?.data?.length()},$type,${imageDataCache?.data}"
        )
        imageDataCache?.size = size
        imageDataCache?.format = format
        Timber.d(
            "postImageDataForCacheId:$requestId,${imageDataCache?.requestId}"
        )
        bytes?.takeIf { imageDataCache?.requestId == requestId }?.let {
            imageDataCache?.data?.append(it, 0, it.size)
        }
        if (imageDataCache?.isImageComplete() == true) {
            if (isNeedCacheType(type)) {
                ImageFileHandler.onReceiveData(
                    AiSpeechEngine.INSTANCE.appContext,
                    requestId,
                    type,
                    imageDataCache?.data?.toByteArray()
                )
            }
            onImagePostForCacheEnd(type, postEnd)
        }
    }

    private fun onImagePostForCacheEnd(
        type: ImageFileHandler.ImageType,
        postEnd: (() -> Unit)? = null
    ) {
        Timber.d("onImagePostForCacheEnd")
        if (isPostImageForOnlineReady()) {
            Timber.d("postEnd Invoke ${postEnd == null}")
            imageDataCache?.let {
                postImageStreamStarted()
                val requestId = postImageBeginForOnline(it.totalSize, it.data.length())
                postImageDataForOnline(
                    requestId = requestId,
                    bytes = it.data.buffer(),
                    format = it.format,
                    size = it.size,
                    fromCache = true,
                    type,
                    postEnd = postEnd
                )
                if (type == ImageFileHandler.ImageType.ORIGINAL) {
                    postEnd?.invoke()
                }
            }
            imageDataCache = null
        } else {
            if (isNeedCacheType(type)) {
                ImageFileHandler.closeFile(requestId())
                postEnd?.invoke()
            }
            Timber.w("it is not UploadMultiModal dialogId.")
        }
    }

    fun postImageOcrData(totalSize: Int, data: ByteArray?) {
        Timber.d("postImageOcrData:${data?.size}}")
        data?.let { imageOcrData.append(data, 0, data.size) }
        if (imageOcrData.length() == totalSize) {
            val validOcrData = imageOcrData.buffer().copyOfRange(0, totalSize)
            imageEventPair.ocr = String(validOcrData)
            Timber.d("imageOcrStr:${imageEventPair.ocr},${imageOcrData.length()}")
            imageOcrData.clear()
            onImageOcrReceived()
        }
    }

    private fun onImageOcrReceived() {
        Timber.d("onImageOcrReceived")
//        if (imageEventPair.imageFileId.isNotEmpty()) {
//            imageUnderstandJob?.cancel()
//            postImageUnderStandDone()
//        }
    }

    fun onImageFileId(dialogId: String?, imageId: String?) {
        Timber.d("onImageFileId:$dialogId,$imageId,${imageEventPair.ocr},$imageDataCache")
        if (requestId() == dialogId) {
            imageId?.let {
                imageEventPair.imageFileId = imageId
                postImageUnderStandDonePrepare()
            }
        } else {
            Timber.d("it is old imageId:$dialogId,${requestId()}")
        }
    }

    private fun postImageUnderStandDone() {
        Timber.d("postImageUnderStandDone:${imageEventPair.imageFileId}")
        AiSpeechEngine.INSTANCE.onImageQuery(dialogId(), requestId())
        if (imageEventPair.imageFileId.isNullOrEmpty()) {
            Timber.d("postImageUnderStand:imageFileId is empty.")
        } else {
            imageEventPair.query?.takeIf { it.isNotEmpty() }?.let {
                queryUserQuery(imageEventPair.dialogId) { query ->
                    postImageUnderStandImpl(query)
                }
            } ?: run { postImageUnderStandImpl(imageEventPair.query) }
        }
    }

    private fun postImageUnderStandImpl(query: String?) {
        val imageUnderstand = ImageUnderstand(listOf(imageEventPair.imageFileId))
        query?.let { imageUnderstand.setQuery(it) }
        val ocr = MultiModal.OCR().apply { setTotalText(imageEventPair.ocr.toString()) }
        imageUnderstand.setOcrInfo(ocr)
        Timber.d("postImageUnderStandImpl called isPush:${imageEventPair.isPush}")
        val param = mapOf(KeyWorld.REQUEST_ID to imageEventPair.requestId)
        val understandId = engine.postImageEvent(
            payload = imageUnderstand,
            requestId = if (imageEventPair.isPush) imageEventPair.dialogId else null,
            isFetchDeviceInfo = imageEventPair.isPush,
            params = param
        )
        imageEventPair.dialogId?.let { imageQueryResMap[understandId] = it }
        updateImageChatRecord(imageEventPair.dialogId, imageEventPair.imageFileId)
        Timber.d("postImageUnderStandImpl:$understandId")
    }

    fun imageQueryDialogId(understandId: String?): String? {
        val dialogId = imageQueryResMap[understandId] ?: understandId
        Timber.d("imageQueryDialogId:$understandId,$dialogId")
        return dialogId
    }

    fun dialogId() = imageEventPair.dialogId

    fun requestId() = imageEventPair.requestId

    fun cleanImageQueryResMap() {
        Timber.d("cleanImageQueryResMap")
        imageQueryResMap.clear()
    }

    private fun isPostImageForOnlineReady(): Boolean {
        return imageEventPair.dialogId?.takeIf { it.isNotEmpty() }?.let {
            true
        } ?: run { false }
    }

    private fun updateImageChatRecord(dialogId: String?, fileId: String?) =
        CoroutineScope(Dispatchers.IO).launch {
            if (dialogId == null || fileId == null) return@launch
            val imageName = StringBuilder().append(dialogId).append(".yuv").toString()
            val record = ChatRecord(
                imageName = imageName,
                fileId = fileId
            )
            boxFor.put(record)
        }
}
