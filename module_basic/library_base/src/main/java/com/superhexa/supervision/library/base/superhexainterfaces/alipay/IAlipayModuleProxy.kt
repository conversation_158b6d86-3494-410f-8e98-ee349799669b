package com.superhexa.supervision.library.base.superhexainterfaces.alipay

import androidx.lifecycle.LifecycleOwner
import com.superhexa.supervision.library.base.basecommon.arouter.IModuleApi

interface IAlipayModuleProxy : IModuleApi {
    fun initAlipay(lifecycleOwner: LifecycleOwner? = null, sn: String)
    fun releaseAlipay()
    fun getIsBinding(): <PERSON><PERSON><PERSON>
    suspend fun getBindStatus(): <PERSON><PERSON><PERSON>
}
